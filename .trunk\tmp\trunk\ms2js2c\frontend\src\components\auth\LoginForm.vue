<template>
  <div class="card">
    <div class="card-header">
      <p class="card-header-title">
        <span class="icon">
          <i class="fas fa-sign-in-alt"></i>
        </span>
        <span>Sign In</span>
      </p>
    </div>
    <div class="card-content">
      <form @submit.prevent="handleSubmit">
        <!-- Email Field -->
        <div class="field">
          <label class="label">Email</label>
          <div class="control has-icons-left">
            <input
              v-model="form.email"
              type="email"
              class="input"
              :class="{ 'is-danger': emailValidation.errors.length > 0 && emailTouched }"
              placeholder="Enter your email"
              @blur="emailTouched = true"
              @input="validateEmail"
              :disabled="authStore.isLoading"
            />
            <span class="icon is-small is-left">
              <i class="fas fa-envelope"></i>
            </span>
          </div>
          <div v-if="emailValidation.errors.length > 0 && emailTouched" class="help is-danger">
            <div v-for="error in emailValidation.errors" :key="error">
              {{ error }}
            </div>
          </div>
        </div>

        <!-- Password Field -->
        <div class="field">
          <label class="label">Password</label>
          <div class="control has-icons-left has-icons-right">
            <input
              v-model="form.password"
              :type="showPassword ? 'text' : 'password'"
              class="input"
              :class="{ 'is-danger': passwordValidation.errors.length > 0 && passwordTouched }"
              placeholder="Enter your password"
              @blur="passwordTouched = true"
              @input="validatePassword"
              :disabled="authStore.isLoading"
            />
            <span class="icon is-small is-left">
              <i class="fas fa-lock"></i>
            </span>
            <span class="icon is-small is-right is-clickable" @click="showPassword = !showPassword">
              <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
            </span>
          </div>
          <div v-if="passwordValidation.errors.length > 0 && passwordTouched" class="help is-danger">
            <div v-for="error in passwordValidation.errors" :key="error">
              {{ error }}
            </div>
          </div>
        </div>

        <!-- Error Message -->
        <div v-if="authStore.error" class="notification is-danger is-light">
          <button class="delete" @click="authStore.error = null"></button>
          {{ authStore.error }}
        </div>

        <!-- Submit Button -->
        <div class="field">
          <div class="control">
            <button
              type="submit"
              class="button is-primary is-fullwidth"
              :class="{ 'is-loading': authStore.isLoading }"
              :disabled="!isFormValid || authStore.isLoading"
            >
              <span class="icon">
                <i class="fas fa-sign-in-alt"></i>
              </span>
              <span>Sign In</span>
            </button>
          </div>
        </div>

        <!-- Links -->
        <div class="field">
          <div class="has-text-centered">
            <router-link to="/forgot-password" class="is-size-7">
              Forgot your password?
            </router-link>
          </div>
        </div>

        <!-- Google Sign-In -->
        <div class="field">
          <div class="has-text-centered mb-3">
            <span class="is-size-7 has-text-grey">or</span>
          </div>
          <GoogleSignInButton 
            button-text="Sign in with Google"
            mode="signin"
            @success="handleGoogleSuccess"
            @error="handleGoogleError"
          />
        </div>

        <hr />

        <div class="field">
          <div class="has-text-centered">
            <p class="is-size-7">
              Don't have an account?
              <router-link to="/register" class="has-text-weight-semibold">
                Sign up
              </router-link>
            </p>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../../stores/auth'
import { validateField, emailRules } from '../../utils/validation'
import type { ValidationResult } from '../../utils/validation'
import GoogleSignInButton from './GoogleSignInButton.vue'

const router = useRouter()
const authStore = useAuthStore()

const form = reactive({
  email: '',
  password: ''
})

const showPassword = ref(false)
const emailTouched = ref(false)
const passwordTouched = ref(false)

const emailValidation = ref<ValidationResult>({ isValid: true, errors: [] })
const passwordValidation = ref<ValidationResult>({ isValid: true, errors: [] })

const validateEmail = () => {
  emailValidation.value = validateField(form.email, emailRules)
}

const validatePassword = () => {
  // For login, we only check if password is provided
  const loginPasswordRules = [
    {
      test: (value: string) => !!value,
      message: 'Password is required'
    }
  ]
  passwordValidation.value = validateField(form.password, loginPasswordRules)
}

const isFormValid = computed(() => {
  return emailValidation.value.isValid && 
         passwordValidation.value.isValid && 
         form.email.trim() !== '' && 
         form.password !== ''
})

const handleSubmit = async () => {
  // Mark all fields as touched
  emailTouched.value = true
  passwordTouched.value = true
  
  // Validate all fields
  validateEmail()
  validatePassword()
  
  if (!isFormValid.value) {
    return
  }

  const result = await authStore.login({
    email: form.email.trim(),
    password: form.password
  })

  if (result.success) {
    // Reset all modal preferences
    authStore.saveModalPreferences({ 
      settingsModalOpen: false,
      searchModalOpen: false,
      shareModalOpen: false
    })
    
    // Redirect to dashboard or intended route
    const redirect = router.currentRoute.value.query.redirect as string
    router.push(redirect || '/dashboard')
  }
}

const handleGoogleSuccess = (message: string) => {
  // Clear any existing errors
  authStore.error = null
  
  // Reset all modal preferences
  authStore.saveModalPreferences({ 
    settingsModalOpen: false,
    searchModalOpen: false,
    shareModalOpen: false
  })
  
  // Redirect to dashboard or intended route
  const redirect = router.currentRoute.value.query.redirect as string
  router.push(redirect || '/dashboard')
}

const handleGoogleError = (error: string) => {
  authStore.error = error
}
</script>

<style scoped>
.is-clickable {
  cursor: pointer;
}

.card {
  max-width: 400px;
  margin: 0 auto;
}
</style>