<template>
  <div class="kanban-editor">
    <!-- Toolbar -->
    <div class="toolbar">
      <div class="toolbar-group">
        <button
          @click="addColumn"
          class="button is-small is-primary"
          title="Add Column"
        >
          <i class="fas fa-plus"></i>
          <span>Add Column</span>
        </button>
        
        <button
          @click="addCard"
          class="button is-small"
          title="Add Card"
          :disabled="!selectedColumnId"
        >
          <i class="fas fa-plus"></i>
          <span>Add Card</span>
        </button>
      </div>

      <div class="toolbar-separator"></div>

      <div class="toolbar-group">
        <button
          @click="showTemplates = true"
          class="button is-small"
          title="Board Templates"
        >
          <i class="fas fa-th-large"></i>
          <span>Templates</span>
        </button>
        
        <button
          @click="showSharing = true"
          class="button is-small"
          title="Share Board"
        >
          <i class="fas fa-share-alt"></i>
          <span>Share</span>
        </button>
      </div>

      <div class="toolbar-separator"></div>

      <div class="toolbar-group">
        <button
          @click="toggleCompactView"
          :class="{ 'is-active': compactView }"
          class="button is-small"
          title="Compact View"
        >
          <i class="fas fa-compress"></i>
        </button>
        
        <button
          @click="exportBoard"
          class="button is-small"
          title="Export Board"
        >
          <i class="fas fa-download"></i>
        </button>
      </div>
    </div>

    <!-- Board Filters -->
    <BoardFilters
      v-model:filters="filters"
      :available-labels="board.labels || []"
      :available-assignees="board.members || []"
      :search-results="searchResults"
      @search="handleSearch"
      @card-selected="selectCardById"
    />

    <!-- Kanban Board -->
    <div class="kanban-board" :class="{ 'compact-view': compactView }">
      <div
        v-for="column in sortedColumns"
        :key="column.id"
        class="kanban-column"
        :class="{ 'selected': selectedColumnId === column.id }"
        @click="selectColumn(column.id)"
        @dragover.prevent
        @drop="onColumnDrop($event, column.id)"
      >
        <!-- Column Header -->
        <div class="column-header">
          <div class="column-title-container">
            <input
              v-if="editingColumnId === column.id"
              v-model="editingColumnTitle"
              @blur="saveColumnTitle(column.id)"
              @keydown.enter="saveColumnTitle(column.id)"
              @keydown.escape="cancelColumnEdit"
              class="input is-small column-title-input"
              ref="columnTitleInput"
            />
            <h3
              v-else
              @dblclick="startColumnEdit(column.id, column.title)"
              class="column-title"
            >
              {{ column.title }}
            </h3>
            <span class="card-count">{{ column.cards.length }}</span>
          </div>
          
          <div class="column-actions">
            <button
              @click.stop="startColumnEdit(column.id, column.title)"
              class="button is-small is-ghost"
              title="Edit Column"
            >
              <i class="fas fa-edit"></i>
            </button>
            
            <button
              @click.stop="deleteColumn(column.id)"
              class="button is-small is-ghost has-text-danger"
              title="Delete Column"
              :disabled="column.cards.length > 0"
            >
              <i class="fas fa-trash"></i>
            </button>
            
            <div
              class="column-drag-handle"
              draggable="true"
              @dragstart="onColumnDragStart($event, column.id)"
              title="Drag to reorder"
            >
              <i class="fas fa-grip-vertical"></i>
            </div>
          </div>
        </div>

        <!-- Cards Container -->
        <div class="cards-container">
          <div
            v-for="card in sortedCards(column.id)"
            :key="card.id"
            class="kanban-card"
            :class="{ 'selected': selectedCardId === card.id }"
            draggable="true"
            @dragstart="onCardDragStart($event, card.id)"
            @click.stop="selectCard(card.id)"
            @dblclick="openCardDetails(card.id)"
          >
            <div class="card-content">
              <h4 class="card-title">{{ card.title }}</h4>
              <p v-if="card.description" class="card-description">
                {{ card.description }}
              </p>
              
              <!-- Card Labels -->
              <div v-if="card.labels && card.labels.length > 0" class="card-labels">
                <span
                  v-for="label in card.labels.slice(0, 3)"
                  :key="label.id"
                  class="tag is-small label-tag"
                  :style="{ backgroundColor: label.color }"
                >
                  {{ label.name }}
                </span>
                <span v-if="card.labels.length > 3" class="tag is-small is-light">
                  +{{ card.labels.length - 3 }}
                </span>
              </div>
              
              <!-- Card Meta -->
              <div class="card-meta">
                <div class="card-meta-left">
                  <!-- Due Date -->
                  <span v-if="card.dueDate" class="card-due-date" :class="getDueDateClass(card.dueDate)">
                    <i class="fas fa-clock"></i>
                    {{ formatDueDate(card.dueDate) }}
                  </span>
                  
                  <!-- Priority -->
                  <span v-if="card.priority && card.priority !== 'low'" class="card-priority" :class="getPriorityClass(card.priority)">
                    <i class="fas fa-exclamation"></i>
                  </span>
                </div>
                
                <div class="card-meta-right">
                  <!-- Attachments -->
                  <span v-if="card.attachments && card.attachments.length > 0" class="card-attachment-count">
                    <i class="fas fa-paperclip"></i>
                    {{ card.attachments.length }}
                  </span>
                  
                  <!-- Comments -->
                  <span v-if="card.comments && card.comments.length > 0" class="card-comment-count">
                    <i class="fas fa-comment"></i>
                    {{ card.comments.length }}
                  </span>
                  
                  <!-- Checklist Progress -->
                  <span v-if="card.checklist && card.checklist.length > 0" class="card-checklist-progress">
                    <i class="fas fa-check-square"></i>
                    {{ getCompletedChecklistItems(card) }}/{{ card.checklist.length }}
                  </span>
                </div>
              </div>
              
              <!-- Assignees -->
              <div v-if="card.assignees && card.assignees.length > 0" class="card-assignees">
                <div
                  v-for="assignee in card.assignees.slice(0, 3)"
                  :key="assignee.id"
                  class="assignee-avatar"
                  :title="assignee.name"
                >
                  <img
                    v-if="assignee.avatar"
                    :src="assignee.avatar"
                    :alt="assignee.name"
                    class="avatar"
                  />
                  <span v-else class="avatar-placeholder">
                    {{ assignee.name.charAt(0).toUpperCase() }}
                  </span>
                </div>
                <div v-if="card.assignees.length > 3" class="assignee-more">
                  +{{ card.assignees.length - 3 }}
                </div>
              </div>
            </div>
            
            <div class="card-actions">
              <button
                @click.stop="editCard(card.id)"
                class="button is-small is-ghost"
                title="Edit Card"
              >
                <i class="fas fa-edit"></i>
              </button>
              
              <button
                @click.stop="deleteCard(card.id)"
                class="button is-small is-ghost has-text-danger"
                title="Delete Card"
              >
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>

          <!-- Add Card Button -->
          <button
            @click="addCardToColumn(column.id)"
            class="add-card-button"
            title="Add Card to Column"
          >
            <i class="fas fa-plus"></i>
            <span>Add a card</span>
          </button>
        </div>
      </div>

      <!-- Add Column Button -->
      <div class="add-column-container">
        <button
          @click="addColumn"
          class="add-column-button"
          title="Add Column"
        >
          <i class="fas fa-plus"></i>
          <span>Add another list</span>
        </button>
      </div>
    </div>

    <!-- Card Details Modal -->
    <CardDetailsModal
      v-if="showCardDetailsModal && selectedCardForDetails"
      :card="selectedCardForDetails"
      :column-title="getColumnTitle(selectedCardForDetails.columnId)"
      :board-members="board.members || []"
      @close="closeCardDetailsModal"
      @update="updateCardFromDetails"
      @delete="deleteCardFromDetails"
      @duplicate="duplicateCardFromDetails"
      @archive="archiveCardFromDetails"
    />

    <!-- Board Templates Modal -->
    <BoardTemplates
      v-if="showTemplates"
      :current-board="board"
      @close="showTemplates = false"
      @apply-template="applyTemplate"
      @save-template="saveTemplate"
    />

    <!-- Board Sharing Modal -->
    <BoardSharing
      v-if="showSharing"
      :share-settings="board.shareSettings || defaultShareSettings"
      :members="board.members || []"
      :current-user-id="currentUserId"
      :board-id="board.id"
      @close="showSharing = false"
      @update:shareSettings="updateShareSettings"
      @invite-member="inviteMember"
      @remove-member="removeMember"
      @update-member-permission="updateMemberPermission"
    />

    <!-- Simple Card Modal (for quick creation) -->
    <div v-if="showCardModal" class="modal is-active">
      <div class="modal-background" @click="closeCardModal"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">
            {{ editingCard ? 'Edit Card' : 'New Card' }}
          </p>
          <button
            class="delete"
            @click="closeCardModal"
            aria-label="close"
          ></button>
        </header>
        
        <section class="modal-card-body">
          <div class="field">
            <label class="label">Title</label>
            <div class="control">
              <input
                v-model="cardForm.title"
                class="input"
                type="text"
                placeholder="Enter card title"
                ref="cardTitleInput"
              />
            </div>
          </div>
          
          <div class="field">
            <label class="label">Description</label>
            <div class="control">
              <textarea
                v-model="cardForm.description"
                class="textarea"
                placeholder="Enter card description (optional)"
                rows="4"
              ></textarea>
            </div>
          </div>
        </section>
        
        <footer class="modal-card-foot">
          <button
            @click="saveCard"
            class="button is-primary"
            :disabled="!cardForm.title.trim()"
          >
            {{ editingCard ? 'Update' : 'Create' }}
          </button>
          <button @click="closeCardModal" class="button">
            Cancel
          </button>
          <button
            v-if="editingCard"
            @click="openCardDetailsFromModal"
            class="button is-info"
          >
            More Details
          </button>
        </footer>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted } from 'vue'
import CardDetailsModal from '../kanban/CardDetailsModal.vue'
import BoardFilters from '../kanban/BoardFilters.vue'
import BoardTemplates from '../kanban/BoardTemplates.vue'
import BoardSharing from '../kanban/BoardSharing.vue'
import type { 
  KanbanBoard, 
  KanbanColumn, 
  KanbanCard, 
  DragEvent as KanbanDragEvent, 
  ColumnDragEvent,
  KanbanFilters,
  CardSearchResult,
  BoardTemplate,
  BoardShareSettings
} from '../../types/kanban'

interface Props {
  modelValue: string
  placeholder?: string
  disabled?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'change', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: 'Start building your kanban board...',
  disabled: false
})

const emit = defineEmits<Emits>()

// Refs
const columnTitleInput = ref<HTMLInputElement>()
const cardTitleInput = ref<HTMLInputElement>()

// Local state
const board = ref<KanbanBoard>({
  id: 'board-1',
  title: 'Kanban Board',
  columns: [],
  labels: [],
  members: [],
  settings: {
    allowComments: true,
    allowAttachments: true,
    cardCoverImages: false,
    votingEnabled: false,
    dueDateReminders: true,
    backgroundColor: '#f5f5f5'
  },
  shareSettings: {
    isPublic: false,
    allowedUsers: [],
    permissions: {}
  },
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
})

// UI state
const selectedColumnId = ref<string | null>(null)
const selectedCardId = ref<string | null>(null)
const editingColumnId = ref<string | null>(null)
const editingColumnTitle = ref('')
const compactView = ref(false)

// Modal state
const showCardModal = ref(false)
const showCardDetailsModal = ref(false)
const showTemplates = ref(false)
const showSharing = ref(false)
const editingCard = ref<KanbanCard | null>(null)
const selectedCardForDetails = ref<KanbanCard | null>(null)
const cardForm = ref({
  title: '',
  description: ''
})

// Filtering and search
const filters = ref<KanbanFilters>({
  search: '',
  labels: [],
  assignees: [],
  priority: [],
  hasAttachments: false,
  hasComments: false
})
const searchResults = ref<CardSearchResult[]>([])

// Current user (mock - in real app, get from auth store)
const currentUserId = ref('current-user-id')

// Drag and drop state
const draggedCardId = ref<string | null>(null)
const draggedColumnId = ref<string | null>(null)

// Computed
const sortedColumns = computed(() => {
  return [...board.value.columns].sort((a, b) => a.position - b.position)
})

const sortedCards = (columnId: string) => {
  const column = board.value.columns.find(col => col.id === columnId)
  if (!column) return []
  return [...column.cards].sort((a, b) => a.position - b.position)
}

// Initialize board from modelValue
const initializeBoard = () => {
  if (props.modelValue) {
    try {
      const parsed = JSON.parse(props.modelValue)
      if (parsed && parsed.columns) {
        board.value = parsed
      }
    } catch (error) {
      console.warn('Failed to parse kanban board data:', error)
      // Initialize with default structure
      initializeDefaultBoard()
    }
  } else {
    initializeDefaultBoard()
  }
}

const initializeDefaultBoard = () => {
  board.value = {
    id: 'board-1',
    title: 'Kanban Board',
    columns: [
      {
        id: generateId(),
        title: 'To Do',
        position: 0,
        boardId: 'board-1',
        cards: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: generateId(),
        title: 'In Progress',
        position: 1,
        boardId: 'board-1',
        cards: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: generateId(),
        title: 'Done',
        position: 2,
        boardId: 'board-1',
        cards: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ],
    labels: [
      { id: generateId(), name: 'Bug', color: '#ff3860' },
      { id: generateId(), name: 'Feature', color: '#3273dc' },
      { id: generateId(), name: 'Urgent', color: '#ff9f43' }
    ],
    members: [
      { id: currentUserId.value, name: 'Current User', email: '<EMAIL>' }
    ],
    settings: {
      allowComments: true,
      allowAttachments: true,
      cardCoverImages: false,
      votingEnabled: false,
      dueDateReminders: true,
      backgroundColor: '#f5f5f5'
    },
    shareSettings: {
      isPublic: false,
      allowedUsers: [],
      permissions: {}
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
  emitChange()
}

// Utility functions
const generateId = () => {
  return Math.random().toString(36).substr(2, 9)
}

const emitChange = () => {
  board.value.updatedAt = new Date().toISOString()
  const serialized = JSON.stringify(board.value)
  emit('update:modelValue', serialized)
  emit('change', serialized)
}

// Column management
const addColumn = () => {
  const newColumn: KanbanColumn = {
    id: generateId(),
    title: `Column ${board.value.columns.length + 1}`,
    position: board.value.columns.length,
    boardId: board.value.id,
    cards: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
  
  board.value.columns.push(newColumn)
  selectedColumnId.value = newColumn.id
  emitChange()
  
  // Start editing the new column title
  nextTick(() => {
    startColumnEdit(newColumn.id, newColumn.title)
  })
}

const deleteColumn = (columnId: string) => {
  const column = board.value.columns.find(col => col.id === columnId)
  if (!column) return
  
  if (column.cards.length > 0) {
    alert('Cannot delete column with cards. Please move or delete all cards first.')
    return
  }
  
  if (confirm(`Are you sure you want to delete "${column.title}"?`)) {
    board.value.columns = board.value.columns.filter(col => col.id !== columnId)
    
    // Update positions
    board.value.columns.forEach((col, index) => {
      col.position = index
    })
    
    if (selectedColumnId.value === columnId) {
      selectedColumnId.value = null
    }
    
    emitChange()
  }
}

const selectColumn = (columnId: string) => {
  selectedColumnId.value = columnId
  selectedCardId.value = null
}

const startColumnEdit = (columnId: string, currentTitle: string) => {
  editingColumnId.value = columnId
  editingColumnTitle.value = currentTitle
  
  nextTick(() => {
    if (columnTitleInput.value && typeof columnTitleInput.value.focus === 'function') {
      columnTitleInput.value.focus()
      if (typeof columnTitleInput.value.select === 'function') {
        columnTitleInput.value.select()
      }
    }
  })
}

const saveColumnTitle = (columnId: string) => {
  const column = board.value.columns.find(col => col.id === columnId)
  if (column && editingColumnTitle.value.trim()) {
    column.title = editingColumnTitle.value.trim()
    column.updatedAt = new Date().toISOString()
    emitChange()
  }
  
  editingColumnId.value = null
  editingColumnTitle.value = ''
}

const cancelColumnEdit = () => {
  editingColumnId.value = null
  editingColumnTitle.value = ''
}

// Enhanced card creation with default values
const createEnhancedCard = (title: string, description: string, columnId: string): KanbanCard => {
  return {
    id: generateId(),
    title: title.trim(),
    description: description.trim() || undefined,
    position: 0, // Will be updated when added to column
    columnId,
    labels: [],
    assignees: [],
    priority: 'medium',
    checklist: [],
    attachments: [],
    comments: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
}

// Card management
const addCard = () => {
  if (!selectedColumnId.value) {
    alert('Please select a column first')
    return
  }
  
  addCardToColumn(selectedColumnId.value)
}

const addCardToColumn = (columnId: string) => {
  editingCard.value = null
  cardForm.value = {
    title: '',
    description: ''
  }
  selectedColumnId.value = columnId
  showCardModal.value = true
  
  nextTick(() => {
    if (cardTitleInput.value && typeof cardTitleInput.value.focus === 'function') {
      cardTitleInput.value.focus()
    }
  })
}

const editCard = (cardId: string) => {
  const card = findCard(cardId)
  if (!card) return
  
  editingCard.value = card
  cardForm.value = {
    title: card.title,
    description: card.description || ''
  }
  showCardModal.value = true
  
  nextTick(() => {
    if (cardTitleInput.value && typeof cardTitleInput.value.focus === 'function') {
      cardTitleInput.value.focus()
      if (typeof cardTitleInput.value.select === 'function') {
        cardTitleInput.value.select()
      }
    }
  })
}

const saveCard = () => {
  if (!cardForm.value.title.trim()) return
  
  if (editingCard.value) {
    // Update existing card
    editingCard.value.title = cardForm.value.title.trim()
    editingCard.value.description = cardForm.value.description.trim() || undefined
    editingCard.value.updatedAt = new Date().toISOString()
  } else {
    // Create new card
    if (!selectedColumnId.value) return
    
    const column = board.value.columns.find(col => col.id === selectedColumnId.value)
    if (!column) return
    
    const newCard = createEnhancedCard(
      cardForm.value.title,
      cardForm.value.description,
      column.id
    )
    newCard.position = column.cards.length
    
    column.cards.push(newCard)
    selectedCardId.value = newCard.id
  }
  
  emitChange()
  closeCardModal()
}

const deleteCard = (cardId: string) => {
  const card = findCard(cardId)
  if (!card) return
  
  if (confirm(`Are you sure you want to delete "${card.title}"?`)) {
    const column = board.value.columns.find(col => col.id === card.columnId)
    if (column) {
      column.cards = column.cards.filter(c => c.id !== cardId)
      
      // Update positions
      column.cards.forEach((c, index) => {
        c.position = index
      })
    }
    
    if (selectedCardId.value === cardId) {
      selectedCardId.value = null
    }
    
    emitChange()
  }
}

const selectCard = (cardId: string) => {
  selectedCardId.value = cardId
}

const findCard = (cardId: string): KanbanCard | null => {
  for (const column of board.value.columns) {
    const card = column.cards.find(c => c.id === cardId)
    if (card) return card
  }
  return null
}

const closeCardModal = () => {
  showCardModal.value = false
  editingCard.value = null
  cardForm.value = {
    title: '',
    description: ''
  }
}

// Enhanced card details modal
const openCardDetails = (cardId: string) => {
  const card = findCard(cardId)
  if (card) {
    selectedCardForDetails.value = card
    showCardDetailsModal.value = true
  }
}

const closeCardDetailsModal = () => {
  showCardDetailsModal.value = false
  selectedCardForDetails.value = null
}

const openCardDetailsFromModal = () => {
  if (editingCard.value) {
    closeCardModal()
    openCardDetails(editingCard.value.id)
  }
}

const updateCardFromDetails = (updatedCard: KanbanCard) => {
  const card = findCard(updatedCard.id)
  if (card) {
    Object.assign(card, updatedCard)
    emitChange()
  }
}

const deleteCardFromDetails = (cardId: string) => {
  deleteCard(cardId)
}

const duplicateCardFromDetails = (card: KanbanCard) => {
  const column = board.value.columns.find(col => col.id === card.columnId)
  if (column) {
    const duplicatedCard = createEnhancedCard(
      `${card.title} (Copy)`,
      card.description || '',
      card.columnId
    )
    
    // Copy enhanced properties
    duplicatedCard.labels = [...card.labels]
    duplicatedCard.assignees = [...card.assignees]
    duplicatedCard.priority = card.priority
    duplicatedCard.checklist = card.checklist.map(item => ({
      ...item,
      id: generateId(),
      completed: false
    }))
    duplicatedCard.position = column.cards.length
    
    column.cards.push(duplicatedCard)
    emitChange()
  }
}

const archiveCardFromDetails = (cardId: string) => {
  // In a real app, this would move the card to an archived state
  // For now, we'll just delete it
  deleteCard(cardId)
}

// Drag and drop functionality
const onCardDragStart = (event: DragEvent, cardId: string) => {
  draggedCardId.value = cardId
  if (event.dataTransfer) {
    event.dataTransfer.effectAllowed = 'move'
    event.dataTransfer.setData('text/plain', cardId)
  }
}

const onColumnDrop = (event: DragEvent, targetColumnId: string) => {
  event.preventDefault()
  
  if (!draggedCardId.value) return
  
  const card = findCard(draggedCardId.value)
  if (!card) return
  
  const sourceColumn = board.value.columns.find(col => col.id === card.columnId)
  const targetColumn = board.value.columns.find(col => col.id === targetColumnId)
  
  if (!sourceColumn || !targetColumn) return
  
  // Remove card from source column
  sourceColumn.cards = sourceColumn.cards.filter(c => c.id !== draggedCardId.value)
  
  // Update positions in source column
  sourceColumn.cards.forEach((c, index) => {
    c.position = index
  })
  
  // Add card to target column
  card.columnId = targetColumnId
  card.position = targetColumn.cards.length
  card.updatedAt = new Date().toISOString()
  targetColumn.cards.push(card)
  
  draggedCardId.value = null
  emitChange()
}

const onColumnDragStart = (event: DragEvent, columnId: string) => {
  draggedColumnId.value = columnId
  if (event.dataTransfer) {
    event.dataTransfer.effectAllowed = 'move'
    event.dataTransfer.setData('text/plain', columnId)
  }
}

// UI actions
const toggleCompactView = () => {
  compactView.value = !compactView.value
}

const exportBoard = () => {
  const dataStr = JSON.stringify(board.value, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(dataBlob)
  
  const link = document.createElement('a')
  link.href = url
  link.download = `kanban-board-${board.value.title.toLowerCase().replace(/\s+/g, '-')}.json`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  
  URL.revokeObjectURL(url)
}

// Utility methods for card display
const formatDueDate = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffDays = Math.ceil((date.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
  
  if (diffDays < 0) return `${Math.abs(diffDays)}d overdue`
  if (diffDays === 0) return 'Today'
  if (diffDays === 1) return 'Tomorrow'
  if (diffDays <= 7) return `${diffDays}d`
  return date.toLocaleDateString()
}

const getDueDateClass = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffDays = Math.ceil((date.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
  
  if (diffDays < 0) return 'is-overdue'
  if (diffDays === 0) return 'is-due-today'
  if (diffDays <= 3) return 'is-due-soon'
  return ''
}

const getPriorityClass = (priority: string) => {
  const classes = {
    low: 'is-success',
    medium: 'is-warning',
    high: 'is-danger',
    urgent: 'is-danger is-urgent'
  }
  return classes[priority as keyof typeof classes] || ''
}

const getCompletedChecklistItems = (card: KanbanCard) => {
  return card.checklist?.filter(item => item.completed).length || 0
}

const getColumnTitle = (columnId: string) => {
  const column = board.value.columns.find(col => col.id === columnId)
  return column?.title || 'Unknown Column'
}

const selectCardById = (cardId: string) => {
  selectedCardId.value = cardId
  const card = findCard(cardId)
  if (card) {
    selectedColumnId.value = card.columnId
  }
}

// Search and filtering
const handleSearch = (query: string) => {
  if (!query.trim()) {
    searchResults.value = []
    return
  }
  
  const results: CardSearchResult[] = []
  const searchTerm = query.toLowerCase()
  
  board.value.columns.forEach(column => {
    column.cards.forEach(card => {
      // Search in title
      if (card.title.toLowerCase().includes(searchTerm)) {
        results.push({
          card,
          columnTitle: column.title,
          matchType: 'title',
          matchText: card.title
        })
      }
      
      // Search in description
      if (card.description && card.description.toLowerCase().includes(searchTerm)) {
        results.push({
          card,
          columnTitle: column.title,
          matchType: 'description',
          matchText: card.description
        })
      }
      
      // Search in comments
      card.comments?.forEach(comment => {
        if (comment.text.toLowerCase().includes(searchTerm)) {
          results.push({
            card,
            columnTitle: column.title,
            matchType: 'comment',
            matchText: comment.text
          })
        }
      })
      
      // Search in checklist
      card.checklist?.forEach(item => {
        if (item.text.toLowerCase().includes(searchTerm)) {
          results.push({
            card,
            columnTitle: column.title,
            matchType: 'checklist',
            matchText: item.text
          })
        }
      })
    })
  })
  
  searchResults.value = results
}

// Template management
const applyTemplate = (template: BoardTemplate) => {
  // Clear existing columns
  board.value.columns = []
  
  // Create columns from template
  template.columns.forEach(templateColumn => {
    const newColumn: KanbanColumn = {
      id: generateId(),
      title: templateColumn.title,
      position: templateColumn.position,
      boardId: board.value.id,
      cards: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    board.value.columns.push(newColumn)
  })
  
  // Apply labels from template
  board.value.labels = [...template.labels]
  
  emitChange()
}

const saveTemplate = (template: BoardTemplate) => {
  // In a real app, this would save to the backend
  console.log('Saving template:', template)
}

// Sharing and collaboration
const defaultShareSettings: BoardShareSettings = {
  isPublic: false,
  allowedUsers: [],
  permissions: {}
}

const updateShareSettings = (settings: BoardShareSettings) => {
  board.value.shareSettings = settings
  emitChange()
}

const inviteMember = (email: string, permission: string) => {
  // In a real app, this would send an invitation email
  const newMember = {
    id: generateId(),
    name: email.split('@')[0], // Mock name from email
    email: email
  }
  
  if (!board.value.members) board.value.members = []
  board.value.members.push(newMember)
  
  if (!board.value.shareSettings) board.value.shareSettings = defaultShareSettings
  board.value.shareSettings.permissions[newMember.id] = permission as any
  
  emitChange()
}

const removeMember = (memberId: string) => {
  if (board.value.members) {
    board.value.members = board.value.members.filter(m => m.id !== memberId)
  }
  
  if (board.value.shareSettings?.permissions) {
    delete board.value.shareSettings.permissions[memberId]
  }
  
  emitChange()
}

const updateMemberPermission = (memberId: string, permission: string) => {
  if (!board.value.shareSettings) board.value.shareSettings = defaultShareSettings
  board.value.shareSettings.permissions[memberId] = permission as any
  emitChange()
}

// Watch for external changes
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue !== JSON.stringify(board.value)) {
      initializeBoard()
    }
  },
  { immediate: true }
)

// Initialize on mount
onMounted(() => {
  if (!props.modelValue || props.modelValue.trim() === '') {
    initializeBoard()
  }
})

// Expose methods for parent components
defineExpose({
  addColumn,
  addCard,
  exportBoard,
  getBoard: () => board.value
})
</script>

<style scoped>
.kanban-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f5f5f5;
}

.toolbar {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: white;
  border-bottom: 1px solid #dbdbdb;
  flex-shrink: 0;
}

.toolbar-group {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.toolbar-separator {
  width: 1px;
  height: 1.5rem;
  background: #dbdbdb;
  margin: 0 0.25rem;
}

.toolbar .button.is-active {
  background: #3273dc;
  color: white;
}

.kanban-board {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  overflow-x: auto;
  flex: 1;
  align-items: flex-start;
}

.kanban-board.compact-view .kanban-column {
  min-width: 200px;
}

.kanban-column {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  min-width: 280px;
  max-width: 280px;
  display: flex;
  flex-direction: column;
  cursor: pointer;
  transition: box-shadow 0.2s ease;
}

.kanban-column:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.kanban-column.selected {
  box-shadow: 0 0 0 2px #3273dc;
}

.column-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
  border-radius: 8px 8px 0 0;
}

.column-title-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
}

.column-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
  cursor: pointer;
}

.column-title-input {
  font-size: 1.1rem;
  font-weight: 600;
  border: none;
  background: transparent;
  padding: 0;
}

.card-count {
  background: #e8e8e8;
  color: #666;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.column-actions {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.column-drag-handle {
  cursor: grab;
  padding: 0.25rem;
  color: #999;
}

.column-drag-handle:hover {
  color: #666;
}

.column-drag-handle:active {
  cursor: grabbing;
}

.cards-container {
  padding: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  min-height: 100px;
  flex: 1;
}

.kanban-card {
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.kanban-card:hover {
  border-color: #3273dc;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.kanban-card.selected {
  border-color: #3273dc;
  box-shadow: 0 0 0 1px #3273dc;
}

.kanban-card:hover .card-actions {
  opacity: 1;
}

.card-content {
  margin-bottom: 0.5rem;
}

.card-title {
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  line-height: 1.3;
}

.card-description {
  font-size: 0.8rem;
  color: #666;
  margin: 0 0 0.5rem 0;
  line-height: 1.4;
}

/* Card Labels */
.card-labels {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  margin-bottom: 0.5rem;
}

.label-tag {
  color: white !important;
  font-weight: 500;
  font-size: 0.7rem;
}

/* Card Meta */
.card-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 0.75rem;
}

.card-meta-left,
.card-meta-right {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.card-due-date {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.125rem 0.375rem;
  border-radius: 12px;
  background: #f0f0f0;
  color: #666;
}

.card-due-date.is-overdue {
  background: #ff3860;
  color: white;
}

.card-due-date.is-due-today {
  background: #ff9f43;
  color: white;
}

.card-due-date.is-due-soon {
  background: #3273dc;
  color: white;
}

.card-priority {
  display: flex;
  align-items: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  justify-content: center;
}

.card-priority.is-success {
  background: #48c774;
  color: white;
}

.card-priority.is-warning {
  background: #ff9f43;
  color: white;
}

.card-priority.is-danger {
  background: #ff3860;
  color: white;
}

.card-priority.is-urgent {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.card-attachment-count,
.card-comment-count,
.card-checklist-progress {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #666;
}

/* Card Assignees */
.card-assignees {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  margin-top: 0.5rem;
}

.assignee-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid white;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.assignee-avatar .avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.assignee-avatar .avatar-placeholder {
  width: 100%;
  height: 100%;
  background: #3273dc;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.7rem;
}

.assignee-more {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #f0f0f0;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: 600;
  border: 2px solid white;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.card-actions {
  display: flex;
  gap: 0.25rem;
  opacity: 0;
  transition: opacity 0.2s ease;
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
}

.card-actions .button {
  padding: 0.25rem;
  min-width: auto;
  height: auto;
}

.add-card-button {
  background: transparent;
  border: 2px dashed #ddd;
  border-radius: 6px;
  padding: 0.75rem;
  color: #999;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.add-card-button:hover {
  border-color: #3273dc;
  color: #3273dc;
  background: rgba(50, 115, 220, 0.05);
}

.add-column-container {
  display: flex;
  align-items: flex-start;
}

.add-column-button {
  background: rgba(255, 255, 255, 0.8);
  border: 2px dashed #ddd;
  border-radius: 8px;
  padding: 1rem;
  color: #999;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  min-width: 280px;
  justify-content: center;
}

.add-column-button:hover {
  border-color: #3273dc;
  color: #3273dc;
  background: rgba(50, 115, 220, 0.05);
}

/* Modal styles */
.modal-card {
  width: 500px;
  max-width: 90vw;
}

.modal-card-body .field:not(:last-child) {
  margin-bottom: 1.5rem;
}

/* Compact view adjustments */
.compact-view .kanban-column {
  min-width: 200px;
  max-width: 200px;
}

.compact-view .card-title {
  font-size: 0.8rem;
}

.compact-view .card-description {
  font-size: 0.75rem;
}

.compact-view .column-header {
  padding: 0.75rem;
}

.compact-view .cards-container {
  padding: 0.25rem;
}

.compact-view .kanban-card {
  padding: 0.5rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .kanban-board {
    padding: 0.5rem;
    gap: 0.5rem;
  }
  
  .kanban-column {
    min-width: 250px;
    max-width: 250px;
  }
  
  .toolbar {
    flex-wrap: wrap;
    gap: 0.25rem;
  }
  
  .toolbar-group {
    gap: 0.125rem;
  }
}

/* Drag and drop visual feedback */
.kanban-card[draggable="true"]:hover {
  cursor: grab;
}

.kanban-card[draggable="true"]:active {
  cursor: grabbing;
  opacity: 0.8;
}

.kanban-column[data-drag-over="true"] {
  background: rgba(50, 115, 220, 0.1);
  border: 2px dashed #3273dc;
}

/* Accessibility improvements */
.kanban-card:focus,
.kanban-column:focus {
  outline: 2px solid #3273dc;
  outline-offset: 2px;
}

.button:focus {
  box-shadow: 0 0 0 0.125em rgba(50, 115, 220, 0.25);
}

/* Animation for card and column creation */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.kanban-card,
.kanban-column {
  animation: slideIn 0.2s ease-out;
}
</style>