<template>
  <div class="export-history">
    <div class="level">
      <div class="level-left">
        <div class="level-item">
          <h3 class="title is-5">
            <i class="fas fa-history mr-2"></i>
            Export History
          </h3>
        </div>
      </div>
      <div class="level-right">
        <div class="level-item">
          <button 
            class="button is-small" 
            @click="refreshHistory"
            :class="{ 'is-loading': isLoading }"
          >
            <i class="fas fa-sync mr-1"></i>
            Refresh
          </button>
        </div>
        <div class="level-item">
          <button 
            class="button is-small is-danger is-outlined" 
            @click="cleanupOldJobs"
            :disabled="isLoading"
          >
            <i class="fas fa-trash mr-1"></i>
            Cleanup Old
          </button>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading && jobs.length === 0" class="has-text-centered py-6">
      <i class="fas fa-spinner fa-spin fa-2x"></i>
      <p class="mt-3">Loading export history...</p>
    </div>

    <!-- Empty State -->
    <div v-else-if="jobs.length === 0" class="has-text-centered py-6">
      <i class="fas fa-download fa-3x has-text-grey-light"></i>
      <p class="mt-3 has-text-grey">No exports yet</p>
      <p class="has-text-grey-light">Your export history will appear here</p>
    </div>

    <!-- Jobs List -->
    <div v-else class="jobs-list">
      <div 
        v-for="job in jobs" 
        :key="job.id"
        class="card mb-4"
      >
        <div class="card-content">
          <div class="level">
            <div class="level-left">
              <div class="level-item">
                <div class="media">
                  <div class="media-left">
                    <i 
                      :class="getFormatIcon(job.format)" 
                      class="fa-2x has-text-grey-light"
                    ></i>
                  </div>
                  <div class="media-content">
                    <p class="title is-6">
                      {{ getFormatDisplayName(job.format) }} Export
                    </p>
                    <p class="subtitle is-7 has-text-grey">
                      {{ job.noteIds.length }} note{{ job.noteIds.length > 1 ? 's' : '' }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div class="level-right">
              <div class="level-item">
                <span 
                  class="tag" 
                  :class="getStatusClass(job.status)"
                >
                  {{ job.status }}
                </span>
              </div>
            </div>
          </div>

          <div class="columns is-mobile">
            <div class="column">
              <p class="is-size-7 has-text-grey">
                <i class="fas fa-calendar mr-1"></i>
                Created: {{ formatDate(job.createdAt) }}
              </p>
              <p v-if="job.completedAt" class="is-size-7 has-text-grey">
                <i class="fas fa-check mr-1"></i>
                Completed: {{ formatDate(job.completedAt) }}
              </p>
              <p v-if="job.error" class="is-size-7 has-text-danger">
                <i class="fas fa-exclamation-triangle mr-1"></i>
                {{ job.error }}
              </p>
            </div>
          </div>

          <!-- Actions -->
          <div class="field is-grouped mt-3">
            <div class="control">
              <button 
                v-if="job.status === 'completed'"
                class="button is-primary is-small"
                @click="downloadJob(job)"
              >
                <i class="fas fa-download mr-1"></i>
                Download
              </button>
              <button 
                v-else-if="job.status === 'processing' || job.status === 'pending'"
                class="button is-info is-small"
                @click="checkJobStatus(job)"
                :class="{ 'is-loading': checkingJobs.has(job.id) }"
              >
                <i class="fas fa-sync mr-1"></i>
                Check Status
              </button>
              <button 
                v-else-if="job.status === 'failed'"
                class="button is-warning is-small"
                @click="retryJob(job)"
              >
                <i class="fas fa-redo mr-1"></i>
                Retry
              </button>
            </div>
            <div class="control">
              <button 
                class="button is-small"
                @click="copyJobId(job.id)"
              >
                <i class="fas fa-copy mr-1"></i>
                Copy ID
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Load More -->
    <div v-if="hasMore" class="has-text-centered mt-4">
      <button 
        class="button is-outlined"
        @click="loadMore"
        :class="{ 'is-loading': isLoadingMore }"
      >
        Load More
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { ExportService, type ExportJob } from '../../services/exportService';

// Reactive state
const jobs = ref<ExportJob[]>([]);
const isLoading = ref(false);
const isLoadingMore = ref(false);
const hasMore = ref(false);
const checkingJobs = ref(new Set<string>());

// Methods
const loadHistory = async (append = false) => {
  try {
    if (!append) {
      isLoading.value = true;
    } else {
      isLoadingMore.value = true;
    }

    const exportJobs = await ExportService.getExportHistory();
    
    if (append) {
      jobs.value.push(...exportJobs);
    } else {
      jobs.value = exportJobs;
    }
    
    // For now, assume no pagination - in a real app you'd implement proper pagination
    hasMore.value = false;
  } catch (error) {
    console.error('Failed to load export history:', error);
  } finally {
    isLoading.value = false;
    isLoadingMore.value = false;
  }
};

const refreshHistory = () => {
  loadHistory(false);
};

const loadMore = () => {
  loadHistory(true);
};

const downloadJob = async (job: ExportJob) => {
  try {
    const blob = await ExportService.downloadExportJob(job.id);
    const filename = ExportService.getExportFilename(
      'notes_export', 
      job.format, 
      true
    );
    
    ExportService.downloadBlob(blob, filename);
  } catch (error) {
    console.error('Download failed:', error);
    // You might want to show a toast notification here
  }
};

const checkJobStatus = async (job: ExportJob) => {
  try {
    checkingJobs.value.add(job.id);
    const updatedJob = await ExportService.getExportJob(job.id);
    
    // Update the job in the list
    const index = jobs.value.findIndex(j => j.id === job.id);
    if (index !== -1) {
      jobs.value[index] = updatedJob;
    }
  } catch (error) {
    console.error('Failed to check job status:', error);
  } finally {
    checkingJobs.value.delete(job.id);
  }
};

const retryJob = async (job: ExportJob) => {
  // In a real implementation, you might want to create a new export job
  // For now, just refresh the status
  await checkJobStatus(job);
};

const copyJobId = async (jobId: string) => {
  try {
    await navigator.clipboard.writeText(jobId);
    // You might want to show a toast notification here
  } catch (error) {
    console.error('Failed to copy job ID:', error);
  }
};

const cleanupOldJobs = async () => {
  try {
    await ExportService.cleanupExportJobs(24); // Clean up jobs older than 24 hours
    await refreshHistory();
  } catch (error) {
    console.error('Failed to cleanup old jobs:', error);
  }
};

const getStatusClass = (status: string) => {
  switch (status) {
    case 'pending':
      return 'is-warning';
    case 'processing':
      return 'is-info';
    case 'completed':
      return 'is-success';
    case 'failed':
      return 'is-danger';
    default:
      return '';
  }
};

const getFormatIcon = (format: string) => {
  return ExportService.getFormatIcon(format);
};

const getFormatDisplayName = (format: string) => {
  return ExportService.getFormatDisplayName(format);
};

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleString();
};

// Lifecycle
onMounted(() => {
  loadHistory();
});
</script>

<style scoped>
.export-history {
  max-height: 600px;
  overflow-y: auto;
}

.jobs-list {
  max-height: 500px;
  overflow-y: auto;
}

.card {
  transition: box-shadow 0.2s ease;
}

.card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.media-left i {
  width: 40px;
  text-align: center;
}

.tag {
  font-weight: 600;
}
</style>