import { httpClient } from '../utils/http'

export interface NoteMetadata {
  wordCount?: number
  readingTime?: number
  lastEditedBy?: string
  collaborators?: string[]
  isFavorite?: boolean
}

export interface Tag {
  id: string
  name: string
  userId: string
  createdAt: string
}

export interface Note {
  id: string
  userId: string
  groupId?: string
  title: string
  content: string
  noteType: 'richtext' | 'markdown' | 'kanban'
  metadata: NoteMetadata
  isArchived: boolean
  createdAt: string
  updatedAt: string
  tags: Tag[]
}

export interface NoteVersion {
  id: string
  noteId: string
  content: string
  versionNumber: number
  createdAt: string
  createdBy: string
}

export interface CreateNoteData {
  title: string
  content: string
  noteType: 'richtext' | 'markdown' | 'kanban'
  groupId?: string
  tags?: string[]
}

export interface UpdateNoteData {
  title?: string
  content?: string
  isArchived?: boolean
  tags?: string[]
  metadata?: Partial<NoteMetadata>
}

export interface NoteFilters {
  noteType?: 'richtext' | 'markdown' | 'kanban'
  tags?: string[]
  isArchived?: boolean
  search?: string
  groupId?: string
}

export interface PaginationOptions {
  page: number
  limit: number
  sortBy?: 'created_at' | 'updated_at' | 'title'
  sortOrder?: 'asc' | 'desc'
}

export interface NotesResponse {
  notes: Note[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

export interface TagsResponse {
  tags: Tag[]
}

export interface VersionsResponse {
  versions: NoteVersion[]
}

class NoteService {
  // Get notes with pagination and filtering
  async getNotes(
    filters: NoteFilters = {}, 
    pagination: PaginationOptions = { page: 1, limit: 20 }
  ): Promise<NotesResponse> {
    const params = new URLSearchParams()
    
    // Add pagination
    params.append('page', pagination.page.toString())
    params.append('limit', pagination.limit.toString())
    
    if (pagination.sortBy) {
      params.append('sortBy', pagination.sortBy)
    }
    
    if (pagination.sortOrder) {
      params.append('sortOrder', pagination.sortOrder)
    }

    // Add filters
    if (filters.noteType) {
      params.append('noteType', filters.noteType)
    }
    
    if (filters.isArchived !== undefined) {
      params.append('isArchived', filters.isArchived.toString())
    }
    
    if (filters.search) {
      params.append('search', filters.search)
    }
    
    if (filters.groupId) {
      params.append('groupId', filters.groupId)
    }
    
    if (filters.tags && filters.tags.length > 0) {
      params.append('tags', filters.tags.join(','))
    }

    const response = await httpClient.get<NotesResponse>(`/notes?${params.toString()}`)
    
    if (response.error) {
      throw new Error(response.error)
    }
    
    return response.data!
  }

  // Get a specific note by ID
  async getNoteById(id: string): Promise<Note> {
    const response = await httpClient.get<Note>(`/notes/${id}`)
    
    if (response.error) {
      throw new Error(response.error)
    }
    
    return response.data!
  }

  // Create a new note
  async createNote(noteData: CreateNoteData): Promise<Note> {
    const response = await httpClient.post<Note>('/notes', noteData)
    
    if (response.error) {
      throw new Error(response.error)
    }
    
    return response.data!
  }

  // Update an existing note
  async updateNote(id: string, updateData: UpdateNoteData): Promise<Note> {
    const response = await httpClient.put<Note>(`/notes/${id}`, updateData)
    
    if (response.error) {
      throw new Error(response.error)
    }
    
    return response.data!
  }

  // Delete a note (soft delete)
  async deleteNote(id: string): Promise<void> {
    const response = await httpClient.delete(`/notes/${id}`)
    
    if (response.error) {
      throw new Error(response.error)
    }
  }

  // Get note version history
  async getNoteVersions(id: string): Promise<NoteVersion[]> {
    const response = await httpClient.get<VersionsResponse>(`/notes/${id}/versions`)
    
    if (response.error) {
      throw new Error(response.error)
    }
    
    return response.data!.versions
  }

  // Get all user tags
  async getTags(): Promise<Tag[]> {
    const response = await httpClient.get<TagsResponse>('/tags')
    
    if (response.error) {
      throw new Error(response.error)
    }
    
    return response.data!.tags
  }

  // Create a new tag
  async createTag(name: string): Promise<Tag> {
    const response = await httpClient.post<Tag>('/tags', { name })
    
    if (response.error) {
      throw new Error(response.error)
    }
    
    return response.data!
  }

  // Auto-save functionality
  private autoSaveTimeouts = new Map<string, number>()

  scheduleAutoSave(
    noteId: string, 
    updateData: UpdateNoteData, 
    delay: number = 2000
  ): Promise<Note> {
    return new Promise((resolve, reject) => {
      // Clear existing timeout for this note
      const existingTimeout = this.autoSaveTimeouts.get(noteId)
      if (existingTimeout) {
        clearTimeout(existingTimeout)
      }

      // Set new timeout
      const timeout = window.setTimeout(async () => {
        try {
          const updatedNote = await this.updateNote(noteId, updateData)
          this.autoSaveTimeouts.delete(noteId)
          resolve(updatedNote)
        } catch (error) {
          this.autoSaveTimeouts.delete(noteId)
          reject(error)
        }
      }, delay)

      this.autoSaveTimeouts.set(noteId, timeout)
    })
  }

  cancelAutoSave(noteId: string): void {
    const timeout = this.autoSaveTimeouts.get(noteId)
    if (timeout) {
      clearTimeout(timeout)
      this.autoSaveTimeouts.delete(noteId)
    }
  }

  // Offline queue functionality
  private offlineQueue: Array<{
    id: string
    action: 'create' | 'update' | 'delete'
    data: any
    timestamp: number
  }> = []

  private isOnline = navigator.onLine

  constructor() {
    // Listen for online/offline events
    window.addEventListener('online', () => {
      this.isOnline = true
      this.processOfflineQueue()
    })

    window.addEventListener('offline', () => {
      this.isOnline = false
    })
  }

  private addToOfflineQueue(action: 'create' | 'update' | 'delete', data: any): void {
    this.offlineQueue.push({
      id: Math.random().toString(36).substr(2, 9),
      action,
      data,
      timestamp: Date.now()
    })

    // Store in localStorage for persistence
    localStorage.setItem('noteOfflineQueue', JSON.stringify(this.offlineQueue))
  }

  private async processOfflineQueue(): Promise<void> {
    if (!this.isOnline || this.offlineQueue.length === 0) {
      return
    }

    const queue = [...this.offlineQueue]
    this.offlineQueue = []

    for (const item of queue) {
      try {
        switch (item.action) {
          case 'create':
            await this.createNote(item.data)
            break
          case 'update':
            await this.updateNote(item.data.id, item.data.updateData)
            break
          case 'delete':
            await this.deleteNote(item.data.id)
            break
        }
      } catch (error) {
        console.error('Failed to process offline queue item:', error)
        // Re-add failed items to queue
        this.offlineQueue.push(item)
      }
    }

    // Update localStorage
    localStorage.setItem('noteOfflineQueue', JSON.stringify(this.offlineQueue))
  }

  // Initialize offline queue from localStorage
  initializeOfflineQueue(): void {
    const stored = localStorage.getItem('noteOfflineQueue')
    if (stored) {
      try {
        this.offlineQueue = JSON.parse(stored)
        if (this.isOnline) {
          this.processOfflineQueue()
        }
      } catch (error) {
        console.error('Failed to parse offline queue:', error)
        localStorage.removeItem('noteOfflineQueue')
      }
    }
  }

  // Offline-aware methods
  async createNoteOfflineAware(noteData: CreateNoteData): Promise<Note | null> {
    if (this.isOnline) {
      return this.createNote(noteData)
    } else {
      this.addToOfflineQueue('create', noteData)
      return null // Will be created when back online
    }
  }

  async updateNoteOfflineAware(id: string, updateData: UpdateNoteData): Promise<Note | null> {
    if (this.isOnline) {
      return this.updateNote(id, updateData)
    } else {
      this.addToOfflineQueue('update', { id, updateData })
      return null // Will be updated when back online
    }
  }

  async deleteNoteOfflineAware(id: string): Promise<void> {
    if (this.isOnline) {
      return this.deleteNote(id)
    } else {
      this.addToOfflineQueue('delete', { id })
    }
  }
}

export const noteService = new NoteService()
export default noteService