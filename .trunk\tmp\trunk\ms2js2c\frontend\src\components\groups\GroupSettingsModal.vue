<template>
  <div class="modal is-active">
    <div class="modal-background" @click="$emit('close')"></div>
    <div class="modal-card">
      <header class="modal-card-head">
        <p class="modal-card-title">Group Settings</p>
        <button class="delete" @click="$emit('close')"></button>
      </header>
      
      <section class="modal-card-body">
        <form @submit.prevent="handleSubmit">
          <!-- Group Name -->
          <div class="field">
            <label class="label">Group Name *</label>
            <div class="control">
              <input
                v-model="form.name"
                class="input"
                :class="{ 'is-danger': errors.name }"
                type="text"
                placeholder="Enter group name"
                maxlength="100"
                required
              />
            </div>
            <p v-if="errors.name" class="help is-danger">{{ errors.name }}</p>
          </div>

          <!-- Description -->
          <div class="field">
            <label class="label">Description</label>
            <div class="control">
              <textarea
                v-model="form.description"
                class="textarea"
                :class="{ 'is-danger': errors.description }"
                placeholder="Describe your group (optional)"
                maxlength="500"
                rows="3"
              ></textarea>
            </div>
            <p v-if="errors.description" class="help is-danger">{{ errors.description }}</p>
          </div>

          <!-- Settings -->
          <div class="field">
            <label class="label">Group Settings</label>
            
            <!-- Allow Member Invites -->
            <div class="field">
              <div class="control">
                <label class="checkbox">
                  <input
                    v-model="form.settings.allowMemberInvites"
                    type="checkbox"
                  />
                  Allow members to invite others
                </label>
              </div>
              <p class="help">When enabled, editors and viewers can invite new members to the group.</p>
            </div>

            <!-- Default Note Permissions -->
            <div class="field">
              <label class="label">Default Note Permissions</label>
              <div class="control">
                <div class="select is-fullwidth">
                  <select v-model="form.settings.defaultNotePermissions">
                    <option value="view">View Only</option>
                    <option value="edit">View and Edit</option>
                  </select>
                </div>
              </div>
              <p class="help">Default permission level for new members when accessing group notes.</p>
            </div>

            <!-- Max Members -->
            <div class="field">
              <label class="label">Maximum Members</label>
              <div class="control">
                <input
                  v-model.number="form.settings.maxMembers"
                  class="input"
                  :class="{ 'is-danger': errors.maxMembers }"
                  type="number"
                  min="1"
                  max="1000"
                  placeholder="50"
                />
              </div>
              <p v-if="errors.maxMembers" class="help is-danger">{{ errors.maxMembers }}</p>
              <p class="help">Maximum number of members allowed in this group (1-1000).</p>
            </div>

            <!-- Require Approval -->
            <div class="field">
              <div class="control">
                <label class="checkbox">
                  <input
                    v-model="form.settings.requireApprovalForJoin"
                    type="checkbox"
                  />
                  Require approval for new members
                </label>
              </div>
              <p class="help">When enabled, new member requests must be approved by an admin.</p>
            </div>
          </div>

          <!-- Danger Zone -->
          <div class="field">
            <label class="label has-text-danger">Danger Zone</label>
            <div class="box has-background-danger-light">
              <div class="field">
                <div class="control">
                  <button 
                    type="button"
                    class="button is-danger is-outlined"
                    @click="showDeleteConfirm = true"
                  >
                    <span class="icon">
                      <i class="fas fa-trash"></i>
                    </span>
                    <span>Delete Group</span>
                  </button>
                </div>
                <p class="help has-text-danger">
                  This action cannot be undone. All group data will be permanently deleted.
                </p>
              </div>
            </div>
          </div>

          <!-- Error Display -->
          <div v-if="submitError" class="notification is-danger">
            {{ submitError }}
          </div>
        </form>
      </section>
      
      <footer class="modal-card-foot">
        <button 
          class="button is-primary"
          :class="{ 'is-loading': loading }"
          :disabled="loading || !isFormValid"
          @click="handleSubmit"
        >
          Save Changes
        </button>
        <button class="button" @click="$emit('close')">Cancel</button>
      </footer>
    </div>

    <!-- Delete Confirmation Modal -->
    <div v-if="showDeleteConfirm" class="modal is-active">
      <div class="modal-background" @click="showDeleteConfirm = false"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title has-text-danger">Delete Group</p>
          <button class="delete" @click="showDeleteConfirm = false"></button>
        </header>
        <section class="modal-card-body">
          <p>Are you sure you want to delete <strong>{{ group.name }}</strong>?</p>
          <p class="has-text-danger mt-2">
            This action cannot be undone. All group data, including notes and member information, will be permanently deleted.
          </p>
          <div class="field mt-4">
            <label class="label">Type the group name to confirm:</label>
            <div class="control">
              <input
                v-model="deleteConfirmText"
                class="input"
                type="text"
                :placeholder="group.name"
              />
            </div>
          </div>
        </section>
        <footer class="modal-card-foot">
          <button 
            class="button is-danger"
            :class="{ 'is-loading': deleteLoading }"
            :disabled="deleteLoading || deleteConfirmText !== group.name"
            @click="handleDelete"
          >
            Delete Group
          </button>
          <button class="button" @click="showDeleteConfirm = false">Cancel</button>
        </footer>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useGroupsStore } from '../../stores/groups';
import type { GroupWithMembers, UpdateGroupData } from '../../types/group';

// Props
const props = defineProps<{
  group: GroupWithMembers;
}>();

// Emits
const emit = defineEmits<{
  close: [];
  updated: [group: GroupWithMembers];
}>();

const router = useRouter();
const groupsStore = useGroupsStore();

// Form data
const form = reactive<UpdateGroupData & { settings: any }>({
  name: '',
  description: '',
  settings: {
    allowMemberInvites: true,
    defaultNotePermissions: 'view',
    requireApprovalForJoin: false,
    maxMembers: 50
  }
});

// Form state
const loading = ref(false);
const submitError = ref<string | null>(null);
const showDeleteConfirm = ref(false);
const deleteLoading = ref(false);
const deleteConfirmText = ref('');
const errors = reactive({
  name: '',
  description: '',
  maxMembers: ''
});

// Computed
const isFormValid = computed(() => {
  return (form.name ?? '').trim().length >= 3 && 
         (form.name ?? '').trim().length <= 100 &&
         (!form.description || form.description.length <= 500) &&
         form.settings.maxMembers >= 1 && 
         form.settings.maxMembers <= 1000;
});

// Methods
const validateForm = (): boolean => {
  // Reset errors
  errors.name = '';
  errors.description = '';
  errors.maxMembers = '';

  let isValid = true;

  // Validate name
  if (!(form.name ?? '').trim()) {
    errors.name = 'Group name is required';
    isValid = false;
  } else if ((form.name ?? '').trim().length < 3) {
    errors.name = 'Group name must be at least 3 characters';
    isValid = false;
  } else if ((form.name ?? '').trim().length > 100) {
    errors.name = 'Group name must be less than 100 characters';
    isValid = false;
  }

  // Validate description
  if (form.description && form.description.length > 500) {
    errors.description = 'Description must be less than 500 characters';
    isValid = false;
  }

  // Validate max members
  if (form.settings.maxMembers < 1) {
    errors.maxMembers = 'Maximum members must be at least 1';
    isValid = false;
  } else if (form.settings.maxMembers > 1000) {
    errors.maxMembers = 'Maximum members cannot exceed 1000';
    isValid = false;
  } else if (form.settings.maxMembers < props.group.memberCount) {
    errors.maxMembers = `Maximum members cannot be less than current member count (${props.group.memberCount})`;
    isValid = false;
  }

  return isValid;
};

const handleSubmit = async () => {
  if (!validateForm()) {
    return;
  }

  try {
    loading.value = true;
    submitError.value = null;

    const updateData: UpdateGroupData = {
      name: (form.name ?? '').trim(),
      description: form.description?.trim() || undefined,
      settings: form.settings
    };

    const updatedGroup = await groupsStore.updateGroup(props.group.id, updateData);
    emit('updated', updatedGroup);
  } catch (error: any) {
    console.error('Error updating group:', error);
    submitError.value = error.message || 'Failed to update group';
  } finally {
    loading.value = false;
  }
};

const handleDelete = async () => {
  try {
    deleteLoading.value = true;
    await groupsStore.deleteGroup(props.group.id);
    
    // Navigate back to groups list
    router.push('/groups');
  } catch (error: any) {
    console.error('Error deleting group:', error);
    submitError.value = error.message || 'Failed to delete group';
    showDeleteConfirm.value = false;
  } finally {
    deleteLoading.value = false;
  }
};

// Initialize form with current group data
onMounted(() => {
  form.name = props.group.name;
  form.description = props.group.description || '';
  form.settings = { ...props.group.settings };
});
</script>

<style scoped>
.modal-card {
  width: 90%;
  max-width: 600px;
}

.help {
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

.box.has-background-danger-light {
  border: 1px solid #f14668;
}
</style>