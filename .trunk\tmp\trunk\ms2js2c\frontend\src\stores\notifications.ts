import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useAuthStore } from './auth'

export interface AdminNotification {
  id: string
  type: 'critical' | 'warning' | 'info' | 'success'
  category: 'content_report' | 'user_action' | 'system' | 'security'
  title: string
  message: string
  read: boolean
  actionUrl?: string
  createdAt: string
  updatedAt?: string
}

interface NotificationFilters {
  type?: string
  category?: string
  read?: string
  page?: number
  limit?: number
}

interface NotificationPagination {
  page: number
  limit: number
  total: number
  totalPages: number
}

export const useNotificationStore = defineStore('notifications', () => {
  const authStore = useAuthStore()
  
  // State
  const notifications = ref<AdminNotification[]>([])
  const pagination = ref<NotificationPagination>({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  })
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const lastRefresh = ref<Date | null>(null)

  // Computed
  const unreadCount = computed(() => 
    notifications.value.filter(n => !n.read).length
  )
  
  const hasUnread = computed(() => unreadCount.value > 0)

  // API helper
  const apiCall = async (endpoint: string, options: RequestInit = {}) => {
    const response = await fetch(`/api/admin/notifications${endpoint}`, {
      ...options,
      headers: {
        'Authorization': `Bearer ${authStore.token}`,
        'Content-Type': 'application/json',
        ...options.headers
      }
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || `HTTP ${response.status}`)
    }

    return response.json()
  }

  // Actions
  const loadNotifications = async (filters: NotificationFilters = {}) => {
    isLoading.value = true
    error.value = null
    
    try {
      const params = new URLSearchParams()
      if (filters.page) params.append('page', filters.page.toString())
      if (filters.limit) params.append('limit', filters.limit.toString())
      if (filters.type) params.append('type', filters.type)
      if (filters.category) params.append('category', filters.category)
      if (filters.read) params.append('read', filters.read)

      const queryString = params.toString()
      const endpoint = queryString ? `?${queryString}` : ''
      
      // Try to use real API, fall back to mock data if not available
      let data
      try {
        data = await apiCall(endpoint)
      } catch (apiError) {
        console.warn('API not available, using mock data:', apiError)
        data = await getMockNotifications(filters)
      }
      
      notifications.value = data.notifications
      pagination.value = data.pagination
      lastRefresh.value = new Date()
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load notifications'
      console.error('Notification loading error:', err)
    } finally {
      isLoading.value = false
    }
  }

  const markAsRead = async (notificationId: string) => {
    try {
      // Try API first, fall back to mock implementation
      try {
        await apiCall(`/${notificationId}/read`, { method: 'PUT' })
      } catch (apiError) {
        console.warn('API not available, using mock implementation:', apiError)
      }
      
      // Update local state
      const notification = notifications.value.find(n => n.id === notificationId)
      if (notification) {
        notification.read = true
        notification.updatedAt = new Date().toISOString()
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to mark notification as read'
      console.error('Mark as read error:', err)
    }
  }

  const markAllAsRead = async () => {
    try {
      // Try API first, fall back to mock implementation
      try {
        await apiCall('/mark-all-read', { method: 'PUT' })
      } catch (apiError) {
        console.warn('API not available, using mock implementation:', apiError)
      }
      
      // Update local state
      notifications.value.forEach(notification => {
        if (!notification.read) {
          notification.read = true
          notification.updatedAt = new Date().toISOString()
        }
      })
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to mark all notifications as read'
      console.error('Mark all as read error:', err)
    }
  }

  const deleteNotification = async (notificationId: string) => {
    try {
      // Try API first, fall back to mock implementation
      try {
        await apiCall(`/${notificationId}`, { method: 'DELETE' })
      } catch (apiError) {
        console.warn('API not available, using mock implementation:', apiError)
      }
      
      // Remove from local state
      notifications.value = notifications.value.filter(n => n.id !== notificationId)
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to delete notification'
      console.error('Delete notification error:', err)
    }
  }

  const refreshNotifications = async () => {
    await loadNotifications({
      page: pagination.value.page
    })
  }

  // Mock data function (to be replaced with actual API)
  const getMockNotifications = async (filters: NotificationFilters = {}) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const allNotifications: AdminNotification[] = [
      {
        id: '1',
        type: 'critical',
        category: 'content_report',
        title: 'High Priority Content Report',
        message: 'New content report flagged as inappropriate content requiring immediate attention',
        read: false,
        actionUrl: '/admin/reports',
        createdAt: new Date(Date.now() - 5 * 60 * 1000).toISOString()
      },
      {
        id: '2',
        type: 'warning',
        category: 'system',
        title: 'High Error Rate Detected',
        message: 'System error rate exceeded 5% in the last hour. Please investigate server performance.',
        read: false,
        actionUrl: '/admin/metrics',
        createdAt: new Date(Date.now() - 15 * 60 * 1000).toISOString()
      },
      {
        id: '3',
        type: 'info',
        category: 'user_action',
        title: 'New User Registration Spike',
        message: '25 new users registered in the last hour, which is above normal activity levels',
        read: true,
        actionUrl: '/admin/users',
        createdAt: new Date(Date.now() - 60 * 60 * 1000).toISOString()
      },
      {
        id: '4',
        type: 'critical',
        category: 'security',
        title: 'Multiple Failed Login Attempts',
        message: 'Detected 50+ failed login attempts from IP ************* in the last 10 minutes',
        read: false,
        actionUrl: '/admin/system',
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
      },
      {
        id: '5',
        type: 'success',
        category: 'system',
        title: 'Backup Completed Successfully',
        message: 'Daily database backup completed successfully at 2:00 AM',
        read: true,
        createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString()
      },
      {
        id: '6',
        type: 'warning',
        category: 'content_report',
        title: 'Content Report Pending Review',
        message: 'Content report #CR-2024-001 has been pending review for over 24 hours',
        read: false,
        actionUrl: '/admin/reports',
        createdAt: new Date(Date.now() - 25 * 60 * 60 * 1000).toISOString()
      },
      {
        id: '7',
        type: 'info',
        category: 'user_action',
        title: 'Group Creation Activity',
        message: '5 new groups created today, bringing total active groups to 127',
        read: true,
        actionUrl: '/admin/users',
        createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        id: '8',
        type: 'warning',
        category: 'system',
        title: 'Storage Usage Alert',
        message: 'Database storage usage has reached 85% capacity. Consider cleanup or expansion.',
        read: false,
        actionUrl: '/admin/system',
        createdAt: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString()
      }
    ]

    // Apply filters
    let filteredNotifications = allNotifications

    if (filters.type) {
      filteredNotifications = filteredNotifications.filter(n => n.type === filters.type)
    }
    
    if (filters.category) {
      filteredNotifications = filteredNotifications.filter(n => n.category === filters.category)
    }
    
    if (filters.read !== undefined && filters.read !== '') {
      const isRead = filters.read === 'true'
      filteredNotifications = filteredNotifications.filter(n => n.read === isRead)
    }

    // Pagination
    const page = filters.page || 1
    const limit = filters.limit || 20
    const total = filteredNotifications.length
    const totalPages = Math.ceil(total / limit)
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit

    const paginatedNotifications = filteredNotifications.slice(startIndex, endIndex)

    return {
      notifications: paginatedNotifications,
      pagination: {
        page,
        limit,
        total,
        totalPages
      }
    }
  }

  return {
    notifications,
    pagination,
    isLoading,
    error,
    lastRefresh,
    unreadCount,
    hasUnread,
    loadNotifications,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    refreshNotifications
  }
})