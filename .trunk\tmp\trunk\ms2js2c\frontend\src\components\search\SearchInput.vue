<template>
  <div class="search-input-container">
    <div class="field has-addons">
      <div class="control has-icons-left is-expanded">
        <input
          ref="searchInputRef"
          v-model="localQuery"
          type="text"
          class="input"
          :class="{ 'is-loading': isSearching }"
          :placeholder="placeholder"
          @input="handleInput"
          @keydown="handleKeydown"
          @focus="handleFocus"
          @blur="handleBlur"
        />
        <span class="icon is-small is-left">
          <i class="fas fa-search"></i>
        </span>
      </div>
      
      <div class="control" v-if="showAdvancedToggle">
        <button
          class="button"
          :class="{ 'is-primary': showAdvanced }"
          @click="toggleAdvanced"
          title="Advanced Search"
        >
          <span class="icon">
            <i class="fas fa-sliders-h"></i>
          </span>
        </button>
      </div>
      
      <div class="control" v-if="localQuery">
        <button
          class="button"
          @click="clearSearch"
          title="Clear Search"
        >
          <span class="icon">
            <i class="fas fa-times"></i>
          </span>
        </button>
      </div>
    </div>

    <!-- Search Suggestions Dropdown -->
    <div 
      v-if="showSuggestions && (suggestions.titles.length > 0 || suggestions.tags.length > 0 || recentSearches.length > 0)"
      class="dropdown is-active"
    >
      <div class="dropdown-menu search-suggestions" role="menu">
        <div class="dropdown-content">
          <!-- Recent Searches -->
          <div v-if="recentSearches.length > 0 && !localQuery" class="suggestion-section">
            <div class="dropdown-item suggestion-header">
              <strong>Recent Searches</strong>
            </div>
            <a
              v-for="recent in recentSearches"
              :key="`recent-${recent}`"
              class="dropdown-item suggestion-item"
              @click="selectSuggestion(recent)"
            >
              <span class="icon is-small">
                <i class="fas fa-history"></i>
              </span>
              <span>{{ recent }}</span>
            </a>
          </div>

          <!-- Title Suggestions -->
          <div v-if="suggestions.titles.length > 0" class="suggestion-section">
            <div class="dropdown-item suggestion-header">
              <strong>Note Titles</strong>
            </div>
            <a
              v-for="title in suggestions.titles"
              :key="`title-${title}`"
              class="dropdown-item suggestion-item"
              @click="selectSuggestion(title)"
            >
              <span class="icon is-small">
                <i class="fas fa-file-alt"></i>
              </span>
              <span v-html="highlightMatch(title, localQuery)"></span>
            </a>
          </div>

          <!-- Tag Suggestions -->
          <div v-if="suggestions.tags.length > 0" class="suggestion-section">
            <div class="dropdown-item suggestion-header">
              <strong>Tags</strong>
            </div>
            <a
              v-for="tag in suggestions.tags"
              :key="`tag-${tag}`"
              class="dropdown-item suggestion-item"
              @click="selectTagSuggestion(tag)"
            >
              <span class="icon is-small">
                <i class="fas fa-tag"></i>
              </span>
              <span v-html="highlightMatch(tag, localQuery)"></span>
            </a>
          </div>

          <!-- Search Tips -->
          <div v-if="localQuery && suggestions.titles.length === 0 && suggestions.tags.length === 0" class="suggestion-section">
            <div class="dropdown-item suggestion-header">
              <strong>Search Tips</strong>
            </div>
            <div class="dropdown-item suggestion-tip">
              <small>
                Try using keywords like <code>type:markdown</code>, <code>tag:work</code>, or <code>"exact phrase"</code>
              </small>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Search Filters Display -->
    <div v-if="activeFilters.length > 0" class="search-filters mt-2">
      <div class="tags">
        <span
          v-for="filter in activeFilters"
          :key="filter.key"
          class="tag is-primary is-light"
        >
          {{ filter.label }}
          <button
            class="delete is-small"
            @click="removeFilter(filter.key)"
          ></button>
        </span>
        <button
          class="tag is-light"
          @click="clearAllFilters"
          title="Clear all filters"
        >
          Clear all
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { useSearchStore } from '../../stores/search'
import type { SearchSuggestions } from '../../services/searchService'

interface Props {
  placeholder?: string
  showAdvancedToggle?: boolean
  autoFocus?: boolean
  debounceMs?: number
}

interface Emits {
  (e: 'search', query: string): void
  (e: 'advanced-toggle', show: boolean): void
  (e: 'suggestion-select', suggestion: string): void
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: 'Search notes...',
  showAdvancedToggle: true,
  autoFocus: false,
  debounceMs: 300
})

const emit = defineEmits<Emits>()

const searchStore = useSearchStore()
const searchInputRef = ref<HTMLInputElement>()
const localQuery = ref('')
const showSuggestions = ref(false)
const suggestions = ref<SearchSuggestions>({ titles: [], tags: [] })
const debounceTimeout = ref<number>()

// Computed
const isSearching = computed(() => searchStore.isSearching)
const showAdvanced = computed(() => searchStore.showAdvancedSearch)
const recentSearches = computed(() => searchStore.recentSearches)

const activeFilters = computed(() => {
  const filters = []
  const searchFilters = searchStore.searchFilters

  if (searchFilters.noteType) {
    filters.push({
      key: 'noteType',
      label: `Type: ${searchFilters.noteType}`
    })
  }

  if (searchFilters.tags && searchFilters.tags.length > 0) {
    filters.push({
      key: 'tags',
      label: `Tags: ${searchFilters.tags.join(', ')}`
    })
  }

  if (searchFilters.dateFrom) {
    filters.push({
      key: 'dateFrom',
      label: `From: ${searchFilters.dateFrom}`
    })
  }

  if (searchFilters.dateTo) {
    filters.push({
      key: 'dateTo',
      label: `To: ${searchFilters.dateTo}`
    })
  }

  if (searchFilters.archived) {
    filters.push({
      key: 'archived',
      label: 'Archived'
    })
  }

  return filters
})

// Methods
const handleInput = () => {
  // Clear existing timeout
  if (debounceTimeout.value) {
    clearTimeout(debounceTimeout.value)
  }

  // Set new timeout for debounced search
  debounceTimeout.value = window.setTimeout(async () => {
    if (localQuery.value.trim()) {
      // Get suggestions
      suggestions.value = await searchStore.getSuggestions(localQuery.value)
      showSuggestions.value = true

      // Perform debounced search
      await searchStore.debouncedSearch(localQuery.value)
      emit('search', localQuery.value)
    } else {
      suggestions.value = { titles: [], tags: [] }
      showSuggestions.value = false
      searchStore.clearSearchResults()
    }
  }, props.debounceMs)
}

const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter') {
    event.preventDefault()
    performSearch()
  } else if (event.key === 'Escape') {
    hideSuggestions()
  }
}

const handleFocus = () => {
  if (localQuery.value.trim()) {
    showSuggestions.value = true
  } else if (recentSearches.value.length > 0) {
    showSuggestions.value = true
  }
}

const handleBlur = () => {
  // Delay hiding suggestions to allow clicking on them
  setTimeout(() => {
    showSuggestions.value = false
  }, 200)
}

const performSearch = async () => {
  if (localQuery.value.trim()) {
    hideSuggestions()
    await searchStore.search(localQuery.value, {}, { page: 1 })
    emit('search', localQuery.value)
  }
}

const selectSuggestion = (suggestion: string) => {
  localQuery.value = suggestion
  hideSuggestions()
  performSearch()
  emit('suggestion-select', suggestion)
}

const selectTagSuggestion = (tag: string) => {
  // Add tag filter instead of replacing query
  const currentTags = searchStore.searchFilters.tags || []
  if (!currentTags.includes(tag)) {
    searchStore.setSearchFilters({
      tags: [...currentTags, tag]
    })
    performSearch()
  }
  hideSuggestions()
}

const hideSuggestions = () => {
  showSuggestions.value = false
}

const clearSearch = () => {
  localQuery.value = ''
  searchStore.resetSearch()
  hideSuggestions()
  searchInputRef.value?.focus()
}

const toggleAdvanced = () => {
  searchStore.toggleAdvancedSearch()
  emit('advanced-toggle', searchStore.showAdvancedSearch)
}

const removeFilter = (filterKey: string) => {
  const filters = { ...searchStore.searchFilters }
  
  switch (filterKey) {
    case 'noteType':
      delete filters.noteType
      break
    case 'tags':
      delete filters.tags
      break
    case 'dateFrom':
      delete filters.dateFrom
      break
    case 'dateTo':
      delete filters.dateTo
      break
    case 'archived':
      delete filters.archived
      break
  }

  searchStore.setSearchFilters(filters)
  if (localQuery.value.trim()) {
    performSearch()
  }
}

const clearAllFilters = () => {
  searchStore.clearSearchFilters()
  if (localQuery.value.trim()) {
    performSearch()
  }
}

const highlightMatch = (text: string, query: string): string => {
  if (!query) return text
  
  const regex = new RegExp(`(${query})`, 'gi')
  return text.replace(regex, '<mark>$1</mark>')
}

const focusInput = () => {
  nextTick(() => {
    searchInputRef.value?.focus()
  })
}

// Watch for external query changes
watch(() => searchStore.searchQuery, (newQuery) => {
  if (newQuery !== localQuery.value) {
    localQuery.value = newQuery
  }
})

// Lifecycle
onMounted(() => {
  if (props.autoFocus) {
    focusInput()
  }
  
  // Load search history
  searchStore.loadSearchHistory()
})

onUnmounted(() => {
  if (debounceTimeout.value) {
    clearTimeout(debounceTimeout.value)
  }
})

// Expose methods for parent components
defineExpose({
  focusInput,
  clearSearch
})
</script>

<style scoped>
.search-input-container {
  position: relative;
}

.search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.suggestion-section:not(:last-child) {
  border-bottom: 1px solid #e0e0e0;
}

.suggestion-header {
  background-color: #f5f5f5;
  font-size: 0.75rem;
  padding: 0.5rem 1rem;
  color: #666;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  transition: background-color 0.2s;
}

.suggestion-item:hover {
  background-color: #f0f0f0;
}

.suggestion-tip {
  padding: 0.5rem 1rem;
  color: #666;
}

.search-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.search-filters .tags {
  margin-bottom: 0;
}

.search-filters .tag {
  margin-bottom: 0.25rem;
}

/* Highlight styling */
:deep(mark) {
  background-color: #ffeb3b;
  padding: 0;
  font-weight: bold;
}

/* Loading state */
.input.is-loading {
  background-image: url("data:image/svg+xml,%3csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill='%23666' d='M10 3.5a6.5 6.5 0 1 0 6.5 6.5h-2a4.5 4.5 0 1 1-4.5-4.5V3.5z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 1rem;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
  .search-suggestions {
    max-height: 200px;
  }
  
  .suggestion-item {
    padding: 0.75rem 1rem;
  }
}
</style>