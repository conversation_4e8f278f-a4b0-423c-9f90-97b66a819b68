<template>
  <div class="rich-text-editor">
    <!-- Toolbar -->
    <div class="toolbar" v-if="editor">
      <div class="toolbar-group">
        <!-- Text formatting -->
        <button
          @click="editor.chain().focus().toggleBold().run()"
          :class="{ 'is-active': editor.isActive('bold') }"
          class="button is-small"
          title="Bold (Ctrl+B)"
        >
          <i class="fas fa-bold"></i>
        </button>
        
        <button
          @click="editor.chain().focus().toggleItalic().run()"
          :class="{ 'is-active': editor.isActive('italic') }"
          class="button is-small"
          title="Italic (Ctrl+I)"
        >
          <i class="fas fa-italic"></i>
        </button>
        
        <button
          @click="editor.chain().focus().toggleUnderline().run()"
          :class="{ 'is-active': editor.isActive('underline') }"
          class="button is-small"
          title="Underline (Ctrl+U)"
        >
          <i class="fas fa-underline"></i>
        </button>
        
        <button
          @click="editor.chain().focus().toggleStrike().run()"
          :class="{ 'is-active': editor.isActive('strike') }"
          class="button is-small"
          title="Strikethrough"
        >
          <i class="fas fa-strikethrough"></i>
        </button>
      </div>

      <div class="toolbar-separator"></div>

      <div class="toolbar-group">
        <!-- Headers -->
        <button
          @click="editor.chain().focus().toggleHeading({ level: 1 }).run()"
          :class="{ 'is-active': editor.isActive('heading', { level: 1 }) }"
          class="button is-small"
          title="Heading 1"
        >
          H1
        </button>
        
        <button
          @click="editor.chain().focus().toggleHeading({ level: 2 }).run()"
          :class="{ 'is-active': editor.isActive('heading', { level: 2 }) }"
          class="button is-small"
          title="Heading 2"
        >
          H2
        </button>
        
        <button
          @click="editor.chain().focus().toggleHeading({ level: 3 }).run()"
          :class="{ 'is-active': editor.isActive('heading', { level: 3 }) }"
          class="button is-small"
          title="Heading 3"
        >
          H3
        </button>
      </div>

      <div class="toolbar-separator"></div>

      <div class="toolbar-group">
        <!-- Lists -->
        <button
          @click="editor.chain().focus().toggleBulletList().run()"
          :class="{ 'is-active': editor.isActive('bulletList') }"
          class="button is-small"
          title="Bullet List"
        >
          <i class="fas fa-list-ul"></i>
        </button>
        
        <button
          @click="editor.chain().focus().toggleOrderedList().run()"
          :class="{ 'is-active': editor.isActive('orderedList') }"
          class="button is-small"
          title="Numbered List"
        >
          <i class="fas fa-list-ol"></i>
        </button>
      </div>

      <div class="toolbar-separator"></div>

      <div class="toolbar-group">
        <!-- Links and Images -->
        <button
          @click="setLink"
          :class="{ 'is-active': editor.isActive('link') }"
          class="button is-small"
          title="Add Link (Ctrl+K)"
        >
          <i class="fas fa-link"></i>
        </button>
        
        <button
          @click="addImage"
          class="button is-small"
          title="Add Image"
        >
          <i class="fas fa-image"></i>
        </button>
        
        <input
          ref="imageUpload"
          type="file"
          accept="image/*"
          @change="handleImageUpload"
          style="display: none"
        />
        
        <button
          @click="triggerImageUpload"
          class="button is-small"
          title="Upload Image"
        >
          <i class="fas fa-upload"></i>
        </button>
      </div>

      <div class="toolbar-separator"></div>

      <div class="toolbar-group">
        <!-- Tables -->
        <button
          @click="insertTable"
          class="button is-small"
          title="Insert Table"
        >
          <i class="fas fa-table"></i>
        </button>
        
        <button
          @click="addColumnBefore"
          :disabled="!editor.isActive('table')"
          class="button is-small"
          title="Add Column Before"
        >
          <i class="fas fa-columns"></i>
        </button>
        
        <button
          @click="deleteTable"
          :disabled="!editor.isActive('table')"
          class="button is-small"
          title="Delete Table"
        >
          <i class="fas fa-trash"></i>
        </button>
      </div>

      <div class="toolbar-separator"></div>

      <div class="toolbar-group">
        <!-- Text Alignment -->
        <button
          @click="editor.chain().focus().setTextAlign('left').run()"
          :class="{ 'is-active': editor.isActive({ textAlign: 'left' }) }"
          class="button is-small"
          title="Align Left"
        >
          <i class="fas fa-align-left"></i>
        </button>
        
        <button
          @click="editor.chain().focus().setTextAlign('center').run()"
          :class="{ 'is-active': editor.isActive({ textAlign: 'center' }) }"
          class="button is-small"
          title="Align Center"
        >
          <i class="fas fa-align-center"></i>
        </button>
        
        <button
          @click="editor.chain().focus().setTextAlign('right').run()"
          :class="{ 'is-active': editor.isActive({ textAlign: 'right' }) }"
          class="button is-small"
          title="Align Right"
        >
          <i class="fas fa-align-right"></i>
        </button>
      </div>

      <div class="toolbar-separator"></div>

      <div class="toolbar-group">
        <!-- Text Color and Highlight -->
        <input
          type="color"
          @change="setTextColor"
          class="color-picker"
          title="Text Color"
          value="#000000"
        />
        
        <button
          @click="editor.chain().focus().toggleHighlight().run()"
          :class="{ 'is-active': editor.isActive('highlight') }"
          class="button is-small"
          title="Highlight"
        >
          <i class="fas fa-highlighter"></i>
        </button>
      </div>

      <div class="toolbar-separator"></div>

      <div class="toolbar-group">
        <!-- Undo/Redo -->
        <button
          @click="editor.chain().focus().undo().run()"
          :disabled="!editor.can().undo()"
          class="button is-small"
          title="Undo (Ctrl+Z)"
        >
          <i class="fas fa-undo"></i>
        </button>
        
        <button
          @click="editor.chain().focus().redo().run()"
          :disabled="!editor.can().redo()"
          class="button is-small"
          title="Redo (Ctrl+Y)"
        >
          <i class="fas fa-redo"></i>
        </button>
      </div>
    </div>

    <!-- Editor Content -->
    <div class="editor-content">
      <editor-content :editor="editor" v-if="editor" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
import { Editor, EditorContent } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import Link from '@tiptap/extension-link'
import Image from '@tiptap/extension-image'
import Underline from '@tiptap/extension-underline'
import { Table } from '@tiptap/extension-table'
import { TableRow } from '@tiptap/extension-table-row'
import { TableCell } from '@tiptap/extension-table-cell'
import { TableHeader } from '@tiptap/extension-table-header'
import { TextStyle } from '@tiptap/extension-text-style'
import { Color } from '@tiptap/extension-color'
import { Highlight } from '@tiptap/extension-highlight'
import { TextAlign } from '@tiptap/extension-text-align'

interface Props {
  modelValue: string
  placeholder?: string
  disabled?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'change', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: 'Start writing...',
  disabled: false
})

const emit = defineEmits<Emits>()

const editor = ref<Editor>()
const imageUpload = ref<HTMLInputElement>()

onMounted(() => {
  // Add keyboard event listener
  document.addEventListener('keydown', handleKeyDown)
  
  editor.value = new Editor({
    content: props.modelValue,
    extensions: [
      StarterKit.configure({
        // Disable extensions we're replacing or customizing
        link: false,
        strike: false,
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'editor-link',
        },
      }),
      Image.configure({
        HTMLAttributes: {
          class: 'editor-image',
        },
      }),
      Underline,
      Table.configure({
        resizable: true,
      }),
      TableRow,
      TableHeader,
      TableCell,
      TextStyle,
      Color,
      Highlight.configure({
        multicolor: true,
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
    ],
    editorProps: {
      attributes: {
        class: 'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none',
        placeholder: props.placeholder,
      },
    },
    onUpdate: ({ editor }) => {
      const html = editor.getHTML()
      const sanitizedHtml = validateContent(html)
      emit('update:modelValue', sanitizedHtml)
      emit('change', sanitizedHtml)
    },
    editable: !props.disabled,
  })
})

onBeforeUnmount(() => {
  // Remove keyboard event listener
  document.removeEventListener('keydown', handleKeyDown)
  
  if (editor.value) {
    editor.value.destroy()
  }
})

// Watch for external content changes
watch(
  () => props.modelValue,
  (newValue) => {
    if (editor.value && editor.value.getHTML() !== newValue) {
      editor.value.commands.setContent(newValue)
    }
  }
)

// Watch for disabled state changes
watch(
  () => props.disabled,
  (disabled) => {
    if (editor.value) {
      editor.value.setEditable(!disabled)
    }
  }
)

// Toolbar methods
const setLink = () => {
  if (!editor.value) return

  const previousUrl = editor.value.getAttributes('link').href
  const url = window.prompt('URL', previousUrl)

  // cancelled
  if (url === null) {
    return
  }

  // empty
  if (url === '') {
    editor.value.chain().focus().extendMarkRange('link').unsetLink().run()
    return
  }

  // update link
  editor.value.chain().focus().extendMarkRange('link').setLink({ href: url }).run()
}

const addImage = () => {
  if (!editor.value) return

  const url = window.prompt('Image URL')

  if (url) {
    editor.value.chain().focus().setImage({ src: url }).run()
  }
}

const triggerImageUpload = () => {
  imageUpload.value?.click()
}

const handleImageUpload = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  
  if (file && editor.value) {
    // Create a local URL for the image
    const url = URL.createObjectURL(file)
    editor.value.chain().focus().setImage({ src: url }).run()
    
    // In a real application, you would upload the file to a server
    // and replace the local URL with the server URL
    console.log('Image file selected:', file.name)
    
    // Reset the input
    target.value = ''
  }
}

const insertTable = () => {
  if (!editor.value) return
  
  editor.value.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run()
}

const addColumnBefore = () => {
  if (!editor.value) return
  
  editor.value.chain().focus().addColumnBefore().run()
}

const deleteTable = () => {
  if (!editor.value) return
  
  editor.value.chain().focus().deleteTable().run()
}

const setTextColor = (event: Event) => {
  const target = event.target as HTMLInputElement
  const color = target.value
  
  if (editor.value) {
    editor.value.chain().focus().setColor(color).run()
  }
}

// Keyboard shortcuts handler
const handleKeyDown = (event: KeyboardEvent) => {
  if (!editor.value) return
  
  const { ctrlKey, metaKey, key } = event
  const isCmd = ctrlKey || metaKey
  
  // Custom keyboard shortcuts
  if (isCmd) {
    switch (key.toLowerCase()) {
      case 'k':
        event.preventDefault()
        setLink()
        break
      case 'b':
        event.preventDefault()
        editor.value.chain().focus().toggleBold().run()
        break
      case 'i':
        event.preventDefault()
        editor.value.chain().focus().toggleItalic().run()
        break
      case 'u':
        event.preventDefault()
        editor.value.chain().focus().toggleUnderline().run()
        break
      case 'z':
        if (event.shiftKey) {
          event.preventDefault()
          editor.value.chain().focus().redo().run()
        } else {
          event.preventDefault()
          editor.value.chain().focus().undo().run()
        }
        break
      case 'y':
        event.preventDefault()
        editor.value.chain().focus().redo().run()
        break
    }
  }
}

// Content validation and sanitization
const validateContent = (content: string): string => {
  // Basic HTML sanitization - remove script tags and dangerous attributes
  const sanitized = content
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/on\w+="[^"]*"/gi, '')
    .replace(/javascript:/gi, '')
  
  return sanitized
}

// Expose editor instance for parent components
defineExpose({
  editor: editor.value,
  focus: () => editor.value?.commands.focus(),
  getHTML: () => editor.value?.getHTML() || '',
  setContent: (content: string) => editor.value?.commands.setContent(content),
})
</script>

<style scoped>
.rich-text-editor {
  border: 1px solid #dbdbdb;
  border-radius: 4px;
  background: white;
}

.toolbar {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  border-bottom: 1px solid #dbdbdb;
  background: #fafafa;
  flex-wrap: wrap;
}

.toolbar-group {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.toolbar-separator {
  width: 1px;
  height: 1.5rem;
  background: #dbdbdb;
  margin: 0 0.25rem;
}

.toolbar .button {
  border: 1px solid transparent;
  background: transparent;
  color: #4a4a4a;
  min-width: 2rem;
  height: 2rem;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toolbar .button:hover {
  background: #e8e8e8;
  border-color: #b5b5b5;
}

.toolbar .button.is-active {
  background: #3273dc;
  color: white;
  border-color: #3273dc;
}

.toolbar .button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.toolbar .button:disabled:hover {
  background: transparent;
  border-color: transparent;
}

.editor-content {
  min-height: 300px;
}

/* TipTap editor styles */
:deep(.ProseMirror) {
  padding: 1rem;
  outline: none;
  min-height: 300px;
  line-height: 1.6;
}

:deep(.ProseMirror p) {
  margin: 0 0 1rem 0;
}

:deep(.ProseMirror p:last-child) {
  margin-bottom: 0;
}

:deep(.ProseMirror h1) {
  font-size: 2rem;
  font-weight: bold;
  margin: 1.5rem 0 1rem 0;
  line-height: 1.2;
}

:deep(.ProseMirror h2) {
  font-size: 1.5rem;
  font-weight: bold;
  margin: 1.25rem 0 0.75rem 0;
  line-height: 1.3;
}

:deep(.ProseMirror h3) {
  font-size: 1.25rem;
  font-weight: bold;
  margin: 1rem 0 0.5rem 0;
  line-height: 1.4;
}

:deep(.ProseMirror ul) {
  list-style-type: disc;
  margin: 1rem 0;
  padding-left: 2rem;
}

:deep(.ProseMirror ol) {
  list-style-type: decimal;
  margin: 1rem 0;
  padding-left: 2rem;
}

:deep(.ProseMirror li) {
  margin: 0.25rem 0;
}

:deep(.ProseMirror strong) {
  font-weight: bold;
}

:deep(.ProseMirror em) {
  font-style: italic;
}

:deep(.ProseMirror u) {
  text-decoration: underline;
}

:deep(.ProseMirror s) {
  text-decoration: line-through;
}

:deep(.ProseMirror .editor-link) {
  color: #3273dc;
  text-decoration: underline;
  cursor: pointer;
}

:deep(.ProseMirror .editor-link:hover) {
  color: #2366d1;
}

:deep(.ProseMirror .editor-image) {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  margin: 1rem 0;
}

:deep(.ProseMirror p.is-editor-empty:first-child::before) {
  content: attr(placeholder);
  float: left;
  color: #adb5bd;
  pointer-events: none;
  height: 0;
}

/* Color picker styles */
.color-picker {
  width: 2rem;
  height: 2rem;
  border: 1px solid #dbdbdb;
  border-radius: 4px;
  cursor: pointer;
  background: none;
  padding: 0;
}

.color-picker:hover {
  border-color: #b5b5b5;
}

/* Table styles */
:deep(.ProseMirror table) {
  border-collapse: collapse;
  margin: 1rem 0;
  overflow: hidden;
  table-layout: fixed;
  width: 100%;
}

:deep(.ProseMirror table td),
:deep(.ProseMirror table th) {
  border: 2px solid #ced4da;
  box-sizing: border-box;
  min-width: 1em;
  padding: 3px 5px;
  position: relative;
  vertical-align: top;
}

:deep(.ProseMirror table th) {
  background-color: #f8f9fa;
  font-weight: bold;
  text-align: left;
}

:deep(.ProseMirror table .selectedCell:after) {
  background: rgba(200, 200, 255, 0.4);
  content: "";
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  pointer-events: none;
  position: absolute;
  z-index: 2;
}

:deep(.ProseMirror table .column-resize-handle) {
  background-color: #adf;
  bottom: -2px;
  position: absolute;
  right: -2px;
  pointer-events: none;
  top: 0;
  width: 4px;
}

/* Highlight styles */
:deep(.ProseMirror mark) {
  background-color: #ffeb3b;
  border-radius: 0.25em;
  box-decoration-break: clone;
  padding: 0.125em 0;
}

/* Text alignment styles */
:deep(.ProseMirror .has-focus) {
  border-radius: 3px;
  box-shadow: 0 0 0 3px #68cef8;
}

/* Color styles */
:deep(.ProseMirror [style*="color"]) {
  /* Preserve inline color styles */
}

/* Focus styles */
.rich-text-editor:focus-within {
  border-color: #3273dc;
  box-shadow: 0 0 0 0.125em rgba(50, 115, 220, 0.25);
}
</style>