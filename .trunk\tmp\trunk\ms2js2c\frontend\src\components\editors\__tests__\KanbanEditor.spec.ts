import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import KanbanEditor from '../KanbanEditor.vue'
import type { KanbanBoard } from '../../../types/kanban'

describe('KanbanEditor', () => {
  let wrapper: any

  const defaultBoard: KanbanBoard = {
    id: 'board-1',
    title: 'Test Board',
    columns: [
      {
        id: 'col-1',
        title: 'To Do',
        position: 0,
        boardId: 'board-1',
        cards: [
          {
            id: 'card-1',
            title: 'Test Card',
            description: 'Test Description',
            position: 0,
            columnId: 'col-1',
            createdAt: '2023-01-01T00:00:00Z',
            updatedAt: '2023-01-01T00:00:00Z'
          }
        ],
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z'
      }
    ],
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z'
  }

  beforeEach(() => {
    wrapper = mount(KanbanEditor, {
      props: {
        modelValue: JSON.stringify(defaultBoard),
        placeholder: 'Test placeholder'
      }
    })
  })

  it('renders correctly', () => {
    expect(wrapper.find('.kanban-editor').exists()).toBe(true)
    expect(wrapper.find('.toolbar').exists()).toBe(true)
    expect(wrapper.find('.kanban-board').exists()).toBe(true)
  })

  it('displays toolbar buttons', () => {
    const toolbar = wrapper.find('.toolbar')
    expect(toolbar.find('button[title="Add Column"]').exists()).toBe(true)
    expect(toolbar.find('button[title="Add Card"]').exists()).toBe(true)
    expect(toolbar.find('button[title="Compact View"]').exists()).toBe(true)
    expect(toolbar.find('button[title="Export Board"]').exists()).toBe(true)
  })

  it('displays columns and cards', () => {
    expect(wrapper.find('.kanban-column').exists()).toBe(true)
    expect(wrapper.find('.column-title').text()).toBe('To Do')
    expect(wrapper.find('.kanban-card').exists()).toBe(true)
    expect(wrapper.find('.card-title').text()).toBe('Test Card')
  })

  it('shows card count in column header', () => {
    expect(wrapper.find('.card-count').text()).toBe('1')
  })

  it('emits update:modelValue when board changes', async () => {
    const addColumnBtn = wrapper.find('button[title="Add Column"]')
    await addColumnBtn.trigger('click')
    
    expect(wrapper.emitted('update:modelValue')).toBeTruthy()
    expect(wrapper.emitted('change')).toBeTruthy()
  })

  it('can add a new column', async () => {
    const addColumnBtn = wrapper.find('button[title="Add Column"]')
    await addColumnBtn.trigger('click')
    
    // Should have 2 columns now (original + new)
    expect(wrapper.findAll('.kanban-column')).toHaveLength(2)
  })

  it('can toggle compact view', async () => {
    const compactBtn = wrapper.find('button[title="Compact View"]')
    await compactBtn.trigger('click')
    
    expect(wrapper.find('.kanban-board').classes()).toContain('compact-view')
  })

  it('opens card modal when add card is clicked', async () => {
    // First select a column
    const column = wrapper.find('.kanban-column')
    await column.trigger('click')
    
    const addCardBtn = wrapper.find('button[title="Add Card"]')
    await addCardBtn.trigger('click')
    
    expect(wrapper.find('.modal.is-active').exists()).toBe(true)
  })

  it('can edit column title on double click', async () => {
    const columnTitle = wrapper.find('.column-title')
    await columnTitle.trigger('dblclick')
    
    expect(wrapper.find('.column-title-input').exists()).toBe(true)
  })

  it('handles empty modelValue gracefully', async () => {
    const emptyWrapper = mount(KanbanEditor, {
      props: {
        modelValue: '',
        placeholder: 'Test placeholder'
      }
    })
    
    // Wait for component to initialize
    await emptyWrapper.vm.$nextTick()
    
    expect(emptyWrapper.find('.kanban-editor').exists()).toBe(true)
    // Should initialize with default columns
    expect(emptyWrapper.findAll('.kanban-column').length).toBeGreaterThanOrEqual(1)
  })

  it('handles invalid JSON modelValue gracefully', async () => {
    const invalidWrapper = mount(KanbanEditor, {
      props: {
        modelValue: 'invalid json',
        placeholder: 'Test placeholder'
      }
    })
    
    // Wait for component to initialize
    await invalidWrapper.vm.$nextTick()
    
    expect(invalidWrapper.find('.kanban-editor').exists()).toBe(true)
    // Should initialize with default columns
    expect(invalidWrapper.findAll('.kanban-column').length).toBeGreaterThanOrEqual(1)
  })

  it('can delete empty columns', async () => {
    // Find a column without cards
    const emptyBoard = {
      ...defaultBoard,
      columns: [
        {
          id: 'col-empty',
          title: 'Empty Column',
          position: 0,
          boardId: 'board-1',
          cards: [],
          createdAt: '2023-01-01T00:00:00Z',
          updatedAt: '2023-01-01T00:00:00Z'
        }
      ]
    }
    
    const emptyWrapper = mount(KanbanEditor, {
      props: {
        modelValue: JSON.stringify(emptyBoard)
      }
    })
    
    // Wait for component to initialize
    await emptyWrapper.vm.$nextTick()
    
    // Mock window.confirm
    window.confirm = vi.fn(() => true)
    
    const deleteBtn = emptyWrapper.find('button[title="Delete Column"]')
    expect(deleteBtn.exists()).toBe(true)
    
    await deleteBtn.trigger('click')
    
    expect(window.confirm).toHaveBeenCalled()
    expect(emptyWrapper.emitted('update:modelValue')).toBeTruthy()
  })

  it('prevents deletion of columns with cards', async () => {
    // Mock window.alert
    window.alert = vi.fn()
    
    const deleteBtn = wrapper.find('button[title="Delete Column"]')
    expect(deleteBtn.exists()).toBe(true)
    
    // The button should be disabled for columns with cards
    expect(deleteBtn.attributes('disabled')).toBeDefined()
  })

  it('can select cards and columns', async () => {
    const column = wrapper.find('.kanban-column')
    await column.trigger('click')
    
    expect(column.classes()).toContain('selected')
    
    const card = wrapper.find('.kanban-card')
    await card.trigger('click')
    
    expect(card.classes()).toContain('selected')
  })

  it('exposes methods to parent components', () => {
    expect(wrapper.vm.addColumn).toBeDefined()
    expect(wrapper.vm.addCard).toBeDefined()
    expect(wrapper.vm.exportBoard).toBeDefined()
    expect(wrapper.vm.getBoard).toBeDefined()
  })
})