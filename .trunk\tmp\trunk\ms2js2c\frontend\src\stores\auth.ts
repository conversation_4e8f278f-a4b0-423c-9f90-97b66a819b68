import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

interface User {
  id: string
  email: string
  displayName: string
  avatarUrl?: string
  emailVerified: boolean
  oauth_provider?: string
  two_fa_secret?: string
  created_at?: string
  preferences?: any
}

interface LoginCredentials {
  email: string
  password: string
}

interface RegisterData {
  email: string
  password: string
  displayName: string
}

interface ResetPasswordData {
  token: string
  password: string
}

export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('auth_token'))
  const refreshToken = ref<string | null>(localStorage.getItem('refresh_token'))
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const isInitialized = ref(false) // Add initialization flag

  const isAuthenticated = computed(() => {
    // User is authenticated if they have both token and user data
    if (token.value && user.value) return true
    
    // Also consider authenticated if we have a token
    // This provides resilience during network issues or initialization delays
    if (token.value) return true
    
    return false
  })
  const isAdmin = computed(() => {
    if (!user.value) {
      // If user data isn't loaded yet but we have a token, 
      // check localStorage for cached admin status
      const cachedUser = localStorage.getItem('cached_user')
      if (cachedUser && token.value) {
        try {
          const userData = JSON.parse(cachedUser)
          return userData.preferences?.isAdmin === true || 
                 userData.preferences?.isAdmin === 1 || 
                 userData.email === '<EMAIL>'
        } catch (error) {
          console.warn('Failed to parse cached user data:', error)
        }
      }
      // If we have a token, assume non-admin until proven otherwise
      // This prevents redirecting logged-in users to login page
      if (token.value) return false
      return false
    }
    return user.value.preferences?.isAdmin === true || 
           user.value.preferences?.isAdmin === 1 || 
           user.value.email === '<EMAIL>'
  })

  const setTokens = (accessToken: string, refreshTokenValue: string) => {
    token.value = accessToken
    refreshToken.value = refreshTokenValue
    localStorage.setItem('auth_token', accessToken)
    localStorage.setItem('refresh_token', refreshTokenValue)
  }
  
  // Save user preferences for modals
  const saveModalPreferences = (preferences: { settingsModalOpen?: boolean, searchModalOpen?: boolean, shareModalOpen?: boolean }) => {
    try {
      if (preferences.settingsModalOpen !== undefined) {
        localStorage.setItem('settingsModalOpen', preferences.settingsModalOpen.toString())
      }
      if (preferences.searchModalOpen !== undefined) {
        localStorage.setItem('searchModalOpen', preferences.searchModalOpen.toString())
      }
      if (preferences.shareModalOpen !== undefined) {
        localStorage.setItem('shareModalOpen', preferences.shareModalOpen.toString())
      }
    } catch (error) {
      console.warn('Failed to save modal preferences:', error)
    }
  }
  
  // Load user preferences for modals
  const loadModalPreferences = () => {
    try {
      const preferences = {
        settingsModalOpen: localStorage.getItem('settingsModalOpen') === 'true',
        searchModalOpen: localStorage.getItem('searchModalOpen') === 'true',
        shareModalOpen: localStorage.getItem('shareModalOpen') === 'true'
      }
      return preferences
    } catch (error) {
      console.warn('Failed to load modal preferences:', error)
      return {
        settingsModalOpen: false,
        searchModalOpen: false,
        shareModalOpen: false
      }
    }
  }
  
  // Save user theme preference
  const saveThemePreference = (theme: 'light' | 'dark' | 'auto') => {
    try {
      localStorage.setItem('theme', theme)
    } catch (error) {
      console.warn('Failed to save theme preference:', error)
    }
  }
  
  // Load user theme preference
  const loadThemePreference = () => {
    try {
      const theme = localStorage.getItem('theme') as 'light' | 'dark' | 'auto' | null
      return theme || 'auto'
    } catch (error) {
      console.warn('Failed to load theme preference:', error)
      return 'auto'
    }
  }

  const clearTokens = () => {
    token.value = null
    refreshToken.value = null
    user.value = null
    localStorage.removeItem('auth_token')
    localStorage.removeItem('refresh_token')
    localStorage.removeItem('cached_user')
    // Clear modal states
    localStorage.removeItem('settingsModalOpen')
    localStorage.removeItem('searchModalOpen')
    localStorage.removeItem('shareModalOpen')
  }

  const login = async (credentials: LoginCredentials) => {
    isLoading.value = true
    error.value = null
    
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || 'Login failed')
      }

      const data = await response.json()
      setTokens(data.tokens.accessToken, data.tokens.refreshToken)
      user.value = data.user
      
      // Cache user data for admin status persistence
      localStorage.setItem('cached_user', JSON.stringify(data.user))
      
      // Ensure settings modal state is cleared
      localStorage.removeItem('settingsModalOpen')
      
      resetInitialization() // Reset flag to allow proper initialization
      
      // Dispatch event to notify other stores about login
      window.dispatchEvent(new CustomEvent('user-logged-in', {
        detail: { userId: data.user.id }
      }))
      
      return { success: true }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Login failed'
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }

  const register = async (userData: RegisterData) => {
    isLoading.value = true
    error.value = null
    
    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: userData.email,
          password: userData.password,
          display_name: userData.displayName
        }),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || 'Registration failed')
      }

      const data = await response.json()
      return { success: true, message: data.message }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Registration failed'
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }

  const logout = async () => {
    isLoading.value = true
    
    try {
      if (token.value) {
        await fetch('/api/auth/logout', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token.value}`,
          },
        })
      }
    } catch (err) {
      console.error('Logout error:', err)
    } finally {
      clearTokens()
      resetInitialization() // Reset initialization flag on logout
      
      // Dispatch event to notify other stores about logout
    window.dispatchEvent(new CustomEvent('user-logged-out'))
    
    // Dispatch event to notify UI components about logout
    window.dispatchEvent(new CustomEvent('auth-logout-complete'))
    
    // Reset all modal preferences
    saveModalPreferences({ 
      settingsModalOpen: false,
      searchModalOpen: false,
      shareModalOpen: false
    })
      
      isLoading.value = false
    }
  }

  const refreshAccessToken = async () => {
    if (!refreshToken.value) {
      clearTokens()
      return false
    }

    try {
      const response = await Promise.race([
        fetch('/api/auth/refresh-token', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ refreshToken: refreshToken.value }),
        }),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Token refresh timeout')), 2000)
        )
      ]) as Response

      if (!response.ok) {
        // Only clear tokens if we get a definitive auth error
        if (response.status === 401 || response.status === 403) {
          clearTokens()
        }
        return false
      }

      const data = await response.json()
      setTokens(data.tokens.accessToken, data.tokens.refreshToken)
      
      // Fetch user profile to update user data with timeout
      try {
        const profileResponse = await Promise.race([
          fetch('/api/auth/profile', {
            headers: {
              'Authorization': `Bearer ${data.tokens.accessToken}`,
            },
          }),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Profile fetch timeout')), 1500)
          )
        ]) as Response
        
        if (profileResponse.ok) {
          const profileData = await profileResponse.json()
          user.value = profileData.user
          // Cache user data for admin status persistence
          localStorage.setItem('cached_user', JSON.stringify(profileData.user))
        } else {
          // Try to use cached user data if profile fetch fails
          const cachedUser = localStorage.getItem('cached_user')
          if (cachedUser) {
            user.value = JSON.parse(cachedUser)
            console.log('Using cached user data due to profile fetch failure')
          }
        }
      } catch (profileError) {
        console.warn('Profile fetch failed, using cached data:', profileError)
        // Try to use cached user data
        const cachedUser = localStorage.getItem('cached_user')
        if (cachedUser) {
          user.value = JSON.parse(cachedUser)
        }
      }
      
      return true
    } catch (err) {
      console.warn('Token refresh failed:', err)
      // Only clear tokens on definitive auth failures, not network timeouts
      if (err instanceof Error && (err.message.includes('401') || err.message.includes('unauthorized') || err.message.includes('403'))) {
        clearTokens()
      }
      return false
    }
  }

  const forgotPassword = async (email: string) => {
    isLoading.value = true
    error.value = null
    
    try {
      const response = await fetch('/api/auth/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to send reset email')
      }

      const data = await response.json()
      return { success: true, message: data.message }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to send reset email'
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }

  const resetPassword = async (resetData: ResetPasswordData) => {
    isLoading.value = true
    error.value = null
    
    try {
      const response = await fetch('/api/auth/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(resetData),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Password reset failed')
      }

      const data = await response.json()
      return { success: true, message: data.message }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Password reset failed'
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }

  const verifyEmail = async (token: string) => {
    isLoading.value = true
    error.value = null
    
    try {
      const response = await fetch('/api/auth/verify-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Email verification failed')
      }

      const data = await response.json()
      if (user.value) {
        user.value.emailVerified = true
      }
      return { success: true, message: data.message }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Email verification failed'
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }

  const googleAuth = async (credential: string) => {
    isLoading.value = true
    error.value = null
    
    try {
      const response = await fetch('/api/auth/google', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ credential }),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || 'Google authentication failed')
      }

      const data = await response.json()
      setTokens(data.tokens.accessToken, data.tokens.refreshToken)
      user.value = data.user
      
      return { success: true, message: data.message }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Google authentication failed'
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }

  const getGoogleAuthUrl = async () => {
    try {
      const response = await fetch('/api/auth/google/url')
      
      if (!response.ok) {
        throw new Error('Failed to get Google auth URL')
      }

      const data = await response.json()
      return { success: true, authUrl: data.authUrl }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to get Google auth URL'
      return { success: false, error: error.value }
    }
  }

  // Update user data (for profile updates)
  const updateUser = (userData: Partial<User>) => {
    if (user.value) {
      user.value = { ...user.value, ...userData }
    }
  }

  // Initialize user from token on store creation
  const initializeAuth = async () => {
    console.log('Starting auth initialization...')
    
    // Prevent re-initialization if already done
    if (isInitialized.value) {
      console.log('Auth already initialized, skipping...')
      return
    }

    // If we don't have a refresh token and no token, we're in guest mode
    if (!refreshToken.value && !token.value) {
      console.log('No tokens found, starting in guest mode')
      isInitialized.value = true
      console.log('Auth initialization completed')
      return
    }

    // If we have a token but no refresh token, use cached data
    if (token.value && !refreshToken.value) {
      console.log('Access token found but no refresh token, using cached user data if available')
      const cachedUser = localStorage.getItem('cached_user')
      if (cachedUser) {
        try {
          user.value = JSON.parse(cachedUser)
          console.log('Using cached user data')
        } catch (parseError) {
          console.warn('Failed to parse cached user data:', parseError)
        }
      }
      isInitialized.value = true
      console.log('Auth initialization completed')
      return
    }

    // Only try to refresh if we have a refresh token
    if (refreshToken.value) {
      try {
        console.log('Attempting to refresh access token...')
        const success = await refreshAccessToken()
        if (!success) {
          console.log('Token refresh failed, trying cached user data')
          // Try to use cached user data before clearing tokens
          const cachedUser = localStorage.getItem('cached_user')
          if (cachedUser && token.value) {
            try {
              user.value = JSON.parse(cachedUser)
              console.log('Using cached user data, tokens preserved')
            } catch (parseError) {
              console.warn('Failed to parse cached user data:', parseError)
              // Don't clear tokens immediately, let the app continue with cached data
            }
          }
        } else {
          console.log('Auth initialized successfully with existing tokens')
        }
      } catch (error) {
        console.warn('Auth initialization error:', error)
        // Try cached user data before giving up
        const cachedUser = localStorage.getItem('cached_user')
        if (cachedUser && token.value) {
          try {
            user.value = JSON.parse(cachedUser)
            console.log('Using cached user data due to initialization error')
          } catch (parseError) {
            console.warn('Failed to parse cached user data:', parseError)
          }
        }
      }
    } else {
      console.log('No refresh token found, starting in guest mode')
    }
    
    // Mark as initialized
    isInitialized.value = true
    console.log('Auth initialization completed')
  }

  // Initialize auth with timeout handling and graceful degradation
  const initializeAuthWithTimeout = async (timeoutMs: number = 1500) => {
    // Prevent re-initialization if already done
    if (isInitialized.value) {
      console.log('Auth already initialized, skipping timeout initialization...')
      return
    }

    try {
      // Shorter timeout for faster fallback to guest mode
      await Promise.race([
        initializeAuth(),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Auth initialization timeout')), timeoutMs)
        )
      ])
      console.log('Auth initialization completed successfully')
    } catch (error) {
      const err = error as unknown as { message?: string }
      console.warn('Auth initialization failed or timed out:', err?.message)
      
      // Only clear tokens if we get a definitive auth failure, not on timeout
      if (err?.message && (err.message.includes('401') || err.message.includes('unauthorized') || err.message.includes('invalid'))) {
        console.log('Clearing tokens due to auth failure')
        clearTokens()
        
        // Dispatch guest mode event
        window.dispatchEvent(new CustomEvent('auth-guest-mode', {
          detail: { reason: 'auth-failure' }
        }))
      } else {
        console.log('Auth timeout - keeping tokens, will retry later')
        // Don't clear tokens on timeout - they might still be valid
        // Dispatch timeout event instead
        window.dispatchEvent(new CustomEvent('auth-timeout', {
          detail: { reason: 'initialization-timeout' }
        }))
      }
      
      // Mark as initialized to prevent retries
      isInitialized.value = true
      
      // Don't throw - allow app to continue
    }
  }

  // Force re-initialization (for logout/login scenarios)
  const resetInitialization = () => {
    isInitialized.value = false
  }

  return {
    user,
    token,
    isAuthenticated,
    isAdmin,
    isLoading,
    error,
    isInitialized, // Export the flag
    login,
    register,
    logout,
    refreshAccessToken,
    forgotPassword,
    resetPassword,
    verifyEmail,
    googleAuth,
    getGoogleAuthUrl,
    initializeAuth,
    initializeAuthWithTimeout,
    resetInitialization, // Export reset method
    updateUser,
    setTokens,
    clearTokens,
    saveModalPreferences,
    loadModalPreferences,
    saveThemePreference,
    loadThemePreference
  }
})