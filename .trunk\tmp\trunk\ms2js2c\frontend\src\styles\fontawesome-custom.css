/* Custom FontAwesome build with only used icons */
/* This reduces the CSS bundle size significantly */

/* Font face declarations */
@font-face {
  font-family: 'Font Awesome 6 Free';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src:
    url('../../node_modules/@fortawesome/fontawesome-free/webfonts/fa-regular-400.woff2')
      format('woff2'),
    url('../../node_modules/@fortawesome/fontawesome-free/webfonts/fa-regular-400.woff')
      format('woff');
}

@font-face {
  font-family: 'Font Awesome 6 Free';
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src:
    url('../../node_modules/@fortawesome/fontawesome-free/webfonts/fa-solid-900.woff2')
      format('woff2'),
    url('../../node_modules/@fortawesome/fontawesome-free/webfonts/fa-solid-900.woff')
      format('woff');
}

@font-face {
  font-family: 'Font Awesome 6 Brands';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src:
    url('../../node_modules/@fortawesome/fontawesome-free/webfonts/fa-brands-400.woff2')
      format('woff2'),
    url('../../node_modules/@fortawesome/fontawesome-free/webfonts/fa-brands-400.woff')
      format('woff');
}

/* Core FontAwesome styles */
.fa,
.fas,
.far,
.fab {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
}

.fas {
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
}

.far {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}

.fab {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}

/* Only include icons that are actually used in the app */
.fa-plus:before {
  content: '\f067';
}
.fa-search:before {
  content: '\f002';
}
.fa-times:before {
  content: '\f00d';
}
.fa-edit:before {
  content: '\f044';
}
.fa-trash:before {
  content: '\f1f8';
}
.fa-share-alt:before {
  content: '\f1e0';
}
.fa-copy:before {
  content: '\f0c5';
}
.fa-archive:before {
  content: '\f187';
}
.fa-star:before {
  content: '\f005';
}
.fa-users:before {
  content: '\f0c0';
}
.fa-cog:before {
  content: '\f013';
}
.fa-home:before {
  content: '\f015';
}
.fa-file-alt:before {
  content: '\f15c';
}
.fa-clock:before {
  content: '\f017';
}
.fa-heart:before {
  content: '\f004';
}
.fa-folder:before {
  content: '\f07b';
}
.fa-user:before {
  content: '\f007';
}
.fa-envelope:before {
  content: '\f0e0';
}
.fa-lock:before {
  content: '\f023';
}
.fa-eye:before {
  content: '\f06e';
}
.fa-eye-slash:before {
  content: '\f070';
}
.fa-download:before {
  content: '\f019';
}
.fa-upload:before {
  content: '\f093';
}
.fa-save:before {
  content: '\f0c7';
}
.fa-print:before {
  content: '\f02f';
}
.fa-undo:before {
  content: '\f0e2';
}
.fa-redo:before {
  content: '\f01e';
}
.fa-bold:before {
  content: '\f032';
}
.fa-italic:before {
  content: '\f033';
}
.fa-underline:before {
  content: '\f0cd';
}
.fa-align-left:before {
  content: '\f036';
}
.fa-align-center:before {
  content: '\f037';
}
.fa-align-right:before {
  content: '\f038';
}
.fa-list-ul:before {
  content: '\f0ca';
}
.fa-list-ol:before {
  content: '\f0cb';
}
.fa-link:before {
  content: '\f0c1';
}
.fa-image:before {
  content: '\f03e';
}
.fa-table:before {
  content: '\f0ce';
}
.fa-code:before {
  content: '\f121';
}
.fa-quote-left:before {
  content: '\f10d';
}
.fa-heading:before {
  content: '\f1dc';
}
.fa-strikethrough:before {
  content: '\f0cc';
}
.fa-superscript:before {
  content: '\f12b';
}
.fa-subscript:before {
  content: '\f12c';
}
.fa-text-height:before {
  content: '\f034';
}
.fa-text-width:before {
  content: '\f035';
}
.fa-palette:before {
  content: '\f53f';
}
.fa-highlighter:before {
  content: '\f591';
}
.fa-columns:before {
  content: '\f0db';
}
.fa-th:before {
  content: '\f00a';
}
.fa-th-list:before {
  content: '\f00b';
}
.fa-grip-vertical:before {
  content: '\f58e';
}
.fa-ellipsis-v:before {
  content: '\f142';
}
.fa-ellipsis-h:before {
  content: '\f141';
}
.fa-chevron-down:before {
  content: '\f078';
}
.fa-chevron-up:before {
  content: '\f077';
}
.fa-chevron-left:before {
  content: '\f053';
}
.fa-chevron-right:before {
  content: '\f054';
}
.fa-arrow-up:before {
  content: '\f062';
}
.fa-arrow-down:before {
  content: '\f063';
}
.fa-arrow-left:before {
  content: '\f060';
}
.fa-arrow-right:before {
  content: '\f061';
}
.fa-sort:before {
  content: '\f0dc';
}
.fa-sort-up:before {
  content: '\f0de';
}
.fa-sort-down:before {
  content: '\f0dd';
}
.fa-sort-alpha-down:before {
  content: '\f15d';
}
.fa-filter:before {
  content: '\f0b0';
}
.fa-calendar:before {
  content: '\f133';
}
.fa-calendar-alt:before {
  content: '\f073';
}
.fa-bell:before {
  content: '\f0f3';
}
.fa-bell-slash:before {
  content: '\f1f6';
}
.fa-comment:before {
  content: '\f075';
}
.fa-comments:before {
  content: '\f086';
}
.fa-thumbs-up:before {
  content: '\f164';
}
.fa-thumbs-down:before {
  content: '\f165';
}
.fa-flag:before {
  content: '\f024';
}
.fa-bookmark:before {
  content: '\f02e';
}
.fa-tag:before {
  content: '\f02b';
}
.fa-tags:before {
  content: '\f02c';
}
.fa-layer-group:before {
  content: '\f5fd';
}
.fa-expand:before {
  content: '\f065';
}
.fa-compress:before {
  content: '\f066';
}
.fa-expand-alt:before {
  content: '\f424';
}
.fa-compress-alt:before {
  content: '\f422';
}
.fa-fullscreen:before {
  content: '\f0b2';
}
.fa-spinner:before {
  content: '\f110';
}
.fa-circle-notch:before {
  content: '\f1ce';
}
.fa-sync:before {
  content: '\f021';
}
.fa-sync-alt:before {
  content: '\f2f1';
}
.fa-check:before {
  content: '\f00c';
}
.fa-check-circle:before {
  content: '\f058';
}
.fa-times-circle:before {
  content: '\f057';
}
.fa-exclamation:before {
  content: '\f12a';
}
.fa-exclamation-circle:before {
  content: '\f06a';
}
.fa-exclamation-triangle:before {
  content: '\f071';
}
.fa-info:before {
  content: '\f129';
}
.fa-info-circle:before {
  content: '\f05a';
}
.fa-question:before {
  content: '\f128';
}
.fa-question-circle:before {
  content: '\f059';
}
.fa-lightbulb:before {
  content: '\f0eb';
}
.fa-database:before {
  content: '\f1c0';
}
.fa-server:before {
  content: '\f233';
}
.fa-chart-line:before {
  content: '\f201';
}
.fa-chart-bar:before {
  content: '\f080';
}
.fa-chart-pie:before {
  content: '\f200';
}
.fa-chart-area:before {
  content: '\f1fe';
}
.fa-tachometer-alt:before {
  content: '\f3fd';
}
.fa-memory:before {
  content: '\f538';
}
.fa-microchip:before {
  content: '\f2db';
}
.fa-hdd:before {
  content: '\f0a0';
}
.fa-wifi:before {
  content: '\f1eb';
}
.fa-signal:before {
  content: '\f012';
}
.fa-battery-full:before {
  content: '\f240';
}
.fa-plug:before {
  content: '\f1e6';
}
.fa-power-off:before {
  content: '\f011';
}
.fa-cube:before {
  content: '\f1b2';
}
.fa-cubes:before {
  content: '\f1b3';
}
.fa-puzzle-piece:before {
  content: '\f12e';
}
.fa-wrench:before {
  content: '\f0ad';
}
.fa-tools:before {
  content: '\f7d9';
}
.fa-hammer:before {
  content: '\f6e3';
}
.fa-screwdriver:before {
  content: '\f54a';
}
.fa-bug:before {
  content: '\f188';
}
.fa-shield-alt:before {
  content: '\f3ed';
}
.fa-key:before {
  content: '\f084';
}
.fa-unlock:before {
  content: '\f09c';
}
.fa-unlock-alt:before {
  content: '\f13e';
}

/* Brand icons */
.fa-google:before {
  content: '\f1a0';
}
.fa-github:before {
  content: '\f09b';
}
.fa-twitter:before {
  content: '\f099';
}
.fa-facebook:before {
  content: '\f09a';
}
.fa-linkedin:before {
  content: '\f08c';
}
.fa-markdown:before {
  content: '\f60f';
}

/* Responsive utilities */
@media screen and (max-width: 768px) {
  .fa-lg {
    font-size: 1.2em;
  }
  .fa-2x {
    font-size: 1.8em;
  }
  .fa-3x {
    font-size: 2.4em;
  }
}

/* Animation classes */
.fa-spin {
  animation: fa-spin 2s infinite linear;
}

.fa-pulse {
  animation: fa-spin 1s infinite steps(8);
}

@keyframes fa-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Icon sizing */
.fa-xs {
  font-size: 0.75em;
}
.fa-sm {
  font-size: 0.875em;
}
.fa-lg {
  font-size: 1.25em;
}
.fa-xl {
  font-size: 1.5em;
}
.fa-2x {
  font-size: 2em;
}
.fa-3x {
  font-size: 3em;
}
.fa-4x {
  font-size: 4em;
}
.fa-5x {
  font-size: 5em;
}

/* Icon positioning */
.fa-fw {
  width: 1.25em;
  text-align: center;
}
.fa-ul {
  list-style-type: none;
  margin-left: 2.5em;
  padding-left: 0;
}
.fa-ul > li {
  position: relative;
}
.fa-li {
  position: absolute;
  left: -2em;
  width: 2em;
  top: 0.14em;
  text-align: center;
}
.fa-li.fa-lg {
  left: -1.8em;
}

/* Icon borders and backgrounds */
.fa-border {
  border: solid 0.08em #eee;
  border-radius: 0.1em;
  padding: 0.2em 0.25em 0.15em;
}

.fa-pull-left {
  float: left;
  margin-right: 0.3em;
}
.fa-pull-right {
  float: right;
  margin-left: 0.3em;
}

/* Icon stacking */
.fa-stack {
  position: relative;
  display: inline-block;
  width: 2em;
  height: 2em;
  line-height: 2em;
  vertical-align: middle;
}
.fa-stack-1x,
.fa-stack-2x {
  position: absolute;
  left: 0;
  width: 100%;
  text-align: center;
}
.fa-stack-1x {
  line-height: inherit;
}
.fa-stack-2x {
  font-size: 2em;
}
.fa-inverse {
  color: #fff;
}
