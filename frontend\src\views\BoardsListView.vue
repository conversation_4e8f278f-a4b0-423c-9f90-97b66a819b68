<template>
  <div class="boards-list-view">
    <div class="container is-fluid">
      <!-- Header -->
      <div class="page-header">
        <div class="level">
          <div class="level-left">
            <div class="level-item">
              <h1 class="title is-2">
                <span class="icon">
                  <i class="fas fa-columns"></i>
                </span>
                <span>Kanban Boards</span>
              </h1>
            </div>
          </div>
          <div class="level-right">
            <div class="level-item">
              <div class="buttons">
                <button 
                  class="button is-primary"
                  @click="createNewBoard"
                >
                  <span class="icon">
                    <i class="fas fa-plus"></i>
                  </span>
                  <span>New Board</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Loading State -->
      <div v-if="isLoading" class="loading-state">
        <div class="has-text-centered">
          <span class="icon is-large">
            <i class="fas fa-spinner fa-pulse"></i>
          </span>
          <p>Loading boards...</p>
        </div>
      </div>

      <!-- Empty State -->
      <div v-else-if="boards.length === 0" class="empty-state">
        <div class="has-text-centered">
          <span class="icon is-large has-text-grey-light">
            <i class="fas fa-columns"></i>
          </span>
          <h3 class="title is-4 has-text-grey">No Boards Yet</h3>
          <p class="subtitle">Create your first Kanban board to organize tasks and projects visually.</p>
          <button 
            class="button is-primary is-large"
            @click="createNewBoard"
          >
            <span class="icon">
              <i class="fas fa-plus"></i>
            </span>
            <span>Create Your First Board</span>
          </button>
        </div>
      </div>

      <!-- Boards Grid -->
      <div v-else class="boards-grid">
        <div 
          v-for="board in boards" 
          :key="board.id"
          class="board-card"
          @click="openBoard(board.id)"
        >
          <div class="card">
            <div class="card-content">
              <div class="board-header">
                <h4 class="board-title">{{ board.title }}</h4>
                <div class="board-actions">
                  <div class="dropdown is-right" :class="{ 'is-active': activeDropdown === board.id }">
                    <div class="dropdown-trigger">
                      <button 
                        class="button is-small is-ghost"
                        @click.stop="toggleDropdown(board.id)"
                      >
                        <i class="fas fa-ellipsis-h"></i>
                      </button>
                    </div>
                    <div class="dropdown-menu">
                      <div class="dropdown-content">
                        <a class="dropdown-item" @click.stop="openBoard(board.id)">
                          <i class="fas fa-eye"></i>
                          Open Board
                        </a>
                        <a class="dropdown-item" @click.stop="editBoard(board)">
                          <i class="fas fa-edit"></i>
                          Edit Settings
                        </a>
                        <a class="dropdown-item" @click.stop="shareBoard(board)">
                          <i class="fas fa-share"></i>
                          Share Board
                        </a>
                        <hr class="dropdown-divider">
                        <a class="dropdown-item has-text-danger" @click.stop="deleteBoard(board)">
                          <i class="fas fa-trash"></i>
                          Delete Board
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <p v-if="board.description" class="board-description">
                {{ truncateText(board.description, 120) }}
              </p>
              
              <div class="board-stats">
                <div class="stat-item">
                  <span class="icon is-small">
                    <i class="fas fa-columns"></i>
                  </span>
                  <span>{{ board.columns.length }} columns</span>
                </div>
                <div class="stat-item">
                  <span class="icon is-small">
                    <i class="fas fa-sticky-note"></i>
                  </span>
                  <span>{{ getTotalCards(board) }} cards</span>
                </div>
                <div class="stat-item">
                  <span class="icon is-small">
                    <i class="fas fa-users"></i>
                  </span>
                  <span>{{ board.members.length }} members</span>
                </div>
              </div>
              
              <div class="board-footer">
                <div class="board-members">
                  <div 
                    v-for="member in board.members.slice(0, 4)" 
                    :key="member.id"
                    class="member-avatar"
                    :title="member.name"
                  >
                    <img v-if="member.avatar" :src="member.avatar" :alt="member.name" />
                    <span v-else>{{ member.name.charAt(0).toUpperCase() }}</span>
                  </div>
                  <div v-if="board.members.length > 4" class="extra-members">
                    +{{ board.members.length - 4 }}
                  </div>
                </div>
                <div class="board-updated">
                  {{ formatDate(board.updatedAt) }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useKanbanStore } from '@/stores/kanban'
import { useNotificationStore } from '@/stores/notifications'
import type { KanbanBoard } from '@/types/kanban'

const router = useRouter()
const kanbanStore = useKanbanStore()
const notificationStore = useNotificationStore()

// Reactive state
const isLoading = ref(false)
const activeDropdown = ref<string | null>(null)

// Computed
const boards = computed(() => kanbanStore.boards)

// Methods
const loadBoards = async () => {
  isLoading.value = true
  try {
    await kanbanStore.fetchBoards()
  } catch (error) {
    notificationStore.addNotification({
      type: 'critical',
      category: 'system',
      title: 'Error',
      message: 'Failed to load boards',
      read: false
    })
  } finally {
    isLoading.value = false
  }
}

const createNewBoard = () => {
  router.push('/boards/new')
}

const openBoard = (boardId: string) => {
  router.push(`/boards/${boardId}`)
}

const editBoard = (board: KanbanBoard) => {
  router.push(`/boards/${board.id}/settings`)
}

const shareBoard = (board: KanbanBoard) => {
  // TODO: Implement share functionality
  notificationStore.addNotification({
    type: 'info',
    category: 'system',
    title: 'Info',
    message: 'Share functionality coming soon!',
    read: false
  })
}

const deleteBoard = async (board: KanbanBoard) => {
  if (confirm(`Are you sure you want to delete "${board.title}"? This action cannot be undone.`)) {
    try {
      await kanbanStore.deleteBoard(board.id)
      notificationStore.addNotification({
        type: 'success',
        category: 'system',
        title: 'Success',
        message: 'Board deleted successfully',
        read: false
      })
    } catch (error) {
      notificationStore.addNotification({
        type: 'critical',
        category: 'system',
        title: 'Error',
        message: 'Failed to delete board',
        read: false
      })
    }
  }
}

const toggleDropdown = (boardId: string) => {
  activeDropdown.value = activeDropdown.value === boardId ? null : boardId
}

const getTotalCards = (board: KanbanBoard) => {
  return board.columns.reduce((total, column) => total + column.cards.length, 0)
}

const truncateText = (text: string, maxLength: number) => {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffTime = Math.abs(now.getTime() - date.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays === 1) return 'Yesterday'
  if (diffDays < 7) return `${diffDays} days ago`
  if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`
  
  return date.toLocaleDateString('en-US', { 
    month: 'short', 
    day: 'numeric',
    year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
  })
}

// Close dropdown when clicking outside
const handleClickOutside = (event: Event) => {
  if (!event.target || !(event.target as Element).closest('.dropdown')) {
    activeDropdown.value = null
  }
}

// Lifecycle
onMounted(() => {
  loadBoards()
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.boards-list-view {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 2rem 0;
}

.page-header {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.loading-state,
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.empty-state .icon.is-large {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.boards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.board-card {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.board-card:hover {
  transform: translateY(-2px);
}

.board-card .card {
  height: 100%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s ease;
}

.board-card:hover .card {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.board-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.board-title {
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
  flex: 1;
  font-size: 1.25rem;
}

.board-actions {
  opacity: 0;
  transition: opacity 0.2s;
  position: relative;
}

.board-card:hover .board-actions {
  opacity: 1;
}

.board-description {
  color: #6c757d;
  font-size: 0.875rem;
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.board-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
  color: #6c757d;
}

.board-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid #f1f3f4;
}

.board-members {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.member-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #007bff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  font-weight: 600;
  overflow: hidden;
  border: 2px solid white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.member-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.extra-members {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #6c757d;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
  border: 2px solid white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.board-updated {
  font-size: 0.875rem;
  color: #6c757d;
}

/* Responsive */
@media (max-width: 768px) {
  .boards-list-view {
    padding: 1rem 0;
  }
  
  .page-header {
    margin: 0 1rem 1rem 1rem;
    padding: 1.5rem;
  }
  
  .boards-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 0 1rem;
  }
  
  .level {
    display: block;
  }
  
  .level-left,
  .level-right {
    margin-bottom: 1rem;
  }
  
  .title.is-2 {
    font-size: 1.75rem;
  }
  
  .board-stats {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .board-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
}
</style>