<template>
  <div class="auth-layout">
    <div class="container">
      <div class="columns is-centered">
        <div class="column is-narrow">
          <RegisterForm />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import RegisterForm from '../components/auth/RegisterForm.vue'
</script>

<style scoped>
.auth-layout {
  min-height: 100vh;
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
}
</style>