{"name": "note-taking-app", "version": "1.0.0", "scripts": {"test": "node run-tests.js", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm run test:unit", "test:watch": "concurrently \"cd backend && npm run test:watch\" \"cd frontend && npx vitest\"", "dev": "concurrently \"cd backend && npm run dev\" \"cd frontend && npm run dev\"", "build": "cd backend && npm run build && cd ../frontend && npm run build"}, "devDependencies": {"concurrently": "^8.2.2"}, "dependencies": {"axios": "^1.11.0"}}