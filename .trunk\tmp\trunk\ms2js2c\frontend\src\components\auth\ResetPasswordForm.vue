<template>
  <div class="card">
    <div class="card-header">
      <p class="card-header-title">
        <span class="icon">
          <i class="fas fa-lock"></i>
        </span>
        <span>Set New Password</span>
      </p>
    </div>
    <div class="card-content">
      <div v-if="!resetSuccess">
        <p class="has-text-grey mb-4">
          Enter your new password below.
        </p>
        
        <form @submit.prevent="handleSubmit">
          <!-- Password Field -->
          <div class="field">
            <label class="label">New Password</label>
            <div class="control has-icons-left has-icons-right">
              <input
                v-model="form.password"
                :type="showPassword ? 'text' : 'password'"
                class="input"
                :class="{ 'is-danger': passwordValidation.errors.length > 0 && passwordTouched }"
                placeholder="Enter your new password"
                @blur="passwordTouched = true"
                @input="validatePassword"
                :disabled="authStore.isLoading"
              />
              <span class="icon is-small is-left">
                <i class="fas fa-lock"></i>
              </span>
              <span class="icon is-small is-right is-clickable" @click="showPassword = !showPassword">
                <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
              </span>
            </div>
            <div v-if="passwordValidation.errors.length > 0 && passwordTouched" class="help is-danger">
              <div v-for="error in passwordValidation.errors" :key="error">
                {{ error }}
              </div>
            </div>
            <div v-else class="help">
              <div class="password-strength">
                <div class="password-requirements">
                  <div class="requirement" :class="{ 'is-success': hasMinLength }">
                    <span class="icon is-small">
                      <i :class="hasMinLength ? 'fas fa-check' : 'fas fa-times'"></i>
                    </span>
                    <span>At least 8 characters</span>
                  </div>
                  <div class="requirement" :class="{ 'is-success': hasUppercase }">
                    <span class="icon is-small">
                      <i :class="hasUppercase ? 'fas fa-check' : 'fas fa-times'"></i>
                    </span>
                    <span>One uppercase letter</span>
                  </div>
                  <div class="requirement" :class="{ 'is-success': hasLowercase }">
                    <span class="icon is-small">
                      <i :class="hasLowercase ? 'fas fa-check' : 'fas fa-times'"></i>
                    </span>
                    <span>One lowercase letter</span>
                  </div>
                  <div class="requirement" :class="{ 'is-success': hasNumber }">
                    <span class="icon is-small">
                      <i :class="hasNumber ? 'fas fa-check' : 'fas fa-times'"></i>
                    </span>
                    <span>One number</span>
                  </div>
                  <div class="requirement" :class="{ 'is-success': hasSpecialChar }">
                    <span class="icon is-small">
                      <i :class="hasSpecialChar ? 'fas fa-check' : 'fas fa-times'"></i>
                    </span>
                    <span>One special character</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Confirm Password Field -->
          <div class="field">
            <label class="label">Confirm New Password</label>
            <div class="control has-icons-left has-icons-right">
              <input
                v-model="form.confirmPassword"
                :type="showConfirmPassword ? 'text' : 'password'"
                class="input"
                :class="{ 'is-danger': confirmPasswordValidation.errors.length > 0 && confirmPasswordTouched }"
                placeholder="Confirm your new password"
                @blur="confirmPasswordTouched = true"
                @input="validateConfirmPassword"
                :disabled="authStore.isLoading"
              />
              <span class="icon is-small is-left">
                <i class="fas fa-lock"></i>
              </span>
              <span class="icon is-small is-right is-clickable" @click="showConfirmPassword = !showConfirmPassword">
                <i :class="showConfirmPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
              </span>
            </div>
            <div v-if="confirmPasswordValidation.errors.length > 0 && confirmPasswordTouched" class="help is-danger">
              <div v-for="error in confirmPasswordValidation.errors" :key="error">
                {{ error }}
              </div>
            </div>
          </div>

          <!-- Error Message -->
          <div v-if="authStore.error" class="notification is-danger is-light">
            <button class="delete" @click="authStore.error = null"></button>
            {{ authStore.error }}
          </div>

          <!-- Submit Button -->
          <div class="field">
            <div class="control">
              <button
                type="submit"
                class="button is-primary is-fullwidth"
                :class="{ 'is-loading': authStore.isLoading }"
                :disabled="!isFormValid || authStore.isLoading"
              >
                <span class="icon">
                  <i class="fas fa-check"></i>
                </span>
                <span>Reset Password</span>
              </button>
            </div>
          </div>
        </form>
      </div>

      <!-- Success State -->
      <div v-else class="has-text-centered">
        <div class="icon is-large has-text-success mb-4">
          <i class="fas fa-check-circle fa-3x"></i>
        </div>
        <h3 class="title is-4">Password Reset Successfully</h3>
        <p class="has-text-grey mb-4">
          Your password has been reset. You can now sign in with your new password.
        </p>
        <router-link to="/login" class="button is-primary">
          <span class="icon">
            <i class="fas fa-sign-in-alt"></i>
          </span>
          <span>Sign In</span>
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useAuthStore } from '../../stores/auth'
import { 
  validateField, 
  passwordRules, 
  confirmPasswordRule
} from '../../utils/validation'
import type { ValidationResult } from '../../utils/validation'

const route = useRoute()
const authStore = useAuthStore()

const form = reactive({
  password: '',
  confirmPassword: ''
})

const showPassword = ref(false)
const showConfirmPassword = ref(false)
const passwordTouched = ref(false)
const confirmPasswordTouched = ref(false)
const resetSuccess = ref(false)
const resetToken = ref('')

const passwordValidation = ref<ValidationResult>({ isValid: true, errors: [] })
const confirmPasswordValidation = ref<ValidationResult>({ isValid: true, errors: [] })

// Password strength indicators
const hasMinLength = computed(() => form.password.length >= 8)
const hasUppercase = computed(() => /[A-Z]/.test(form.password))
const hasLowercase = computed(() => /[a-z]/.test(form.password))
const hasNumber = computed(() => /\d/.test(form.password))
const hasSpecialChar = computed(() => /[!@#$%^&*(),.?":{}|<>]/.test(form.password))

const validatePassword = () => {
  passwordValidation.value = validateField(form.password, passwordRules)
  // Re-validate confirm password when password changes
  if (confirmPasswordTouched.value) {
    validateConfirmPassword()
  }
}

const validateConfirmPassword = () => {
  const rules = [confirmPasswordRule(form.password)]
  confirmPasswordValidation.value = validateField(form.confirmPassword, rules)
}

const isFormValid = computed(() => {
  return passwordValidation.value.isValid && 
         confirmPasswordValidation.value.isValid &&
         form.password !== '' &&
         form.confirmPassword !== ''
})

const handleSubmit = async () => {
  passwordTouched.value = true
  confirmPasswordTouched.value = true
  
  validatePassword()
  validateConfirmPassword()
  
  if (!isFormValid.value) {
    return
  }

  const result = await authStore.resetPassword({
    token: resetToken.value,
    password: form.password
  })

  if (result.success) {
    resetSuccess.value = true
  }
}

onMounted(() => {
  // Get reset token from URL query parameter
  resetToken.value = route.query.token as string || ''
  
  if (!resetToken.value) {
    authStore.error = 'Invalid or missing reset token'
  }
})
</script>

<style scoped>
.is-clickable {
  cursor: pointer;
}

.card {
  max-width: 400px;
  margin: 0 auto;
}

.password-requirements {
  font-size: 0.75rem;
  margin-top: 0.5rem;
}

.requirement {
  display: flex;
  align-items: center;
  margin-bottom: 0.25rem;
  color: #999;
}

.requirement.is-success {
  color: #48c774;
}

.requirement .icon {
  margin-right: 0.5rem;
}
</style>