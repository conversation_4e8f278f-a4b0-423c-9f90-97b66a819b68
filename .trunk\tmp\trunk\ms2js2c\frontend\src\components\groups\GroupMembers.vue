<template>
  <div class="group-members">
    <div class="table-container">
      <table class="table is-fullwidth is-hoverable">
        <thead>
          <tr>
            <th>Member</th>
            <th>Role</th>
            <th>Joined</th>
            <th v-if="canManageMembers" class="has-text-right">Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="member in group.members" :key="member.userId">
            <td>
              <div class="media">
                <div class="media-left">
                  <figure class="image is-32x32">
                    <img 
                      v-if="member.avatarUrl" 
                      :src="member.avatarUrl" 
                      :alt="member.displayName"
                      class="is-rounded"
                    />
                    <div 
                      v-else 
                      class="has-background-grey-light has-text-grey-dark is-flex is-align-items-center is-justify-content-center is-rounded"
                      style="width: 32px; height: 32px;"
                    >
                      {{ getInitials(member.displayName) }}
                    </div>
                  </figure>
                </div>
                <div class="media-content">
                  <div class="content">
                    <p>
                      <strong>{{ member.displayName }}</strong>
                      <br>
                      <small class="has-text-grey">{{ member.email }}</small>
                    </p>
                  </div>
                </div>
              </div>
            </td>
            <td>
              <span 
                class="tag"
                :class="getRoleColor(member.role)"
              >
                {{ getRoleDisplayName(member.role) }}
              </span>
              <span v-if="member.userId === group.ownerId" class="tag is-light ml-2">
                Owner
              </span>
            </td>
            <td>
              <span class="has-text-grey">
                {{ formatDate(member.joinedAt) }}
              </span>
            </td>
            <td v-if="canManageMembers" class="has-text-right">
              <div class="dropdown" :class="{ 'is-active': activeDropdown === member.userId }">
                <div class="dropdown-trigger">
                  <button 
                    class="button is-small is-ghost"
                    @click="toggleDropdown(member.userId)"
                    :disabled="member.userId === group.ownerId"
                  >
                    <span class="icon">
                      <i class="fas fa-ellipsis-v"></i>
                    </span>
                  </button>
                </div>
                <div class="dropdown-menu">
                  <div class="dropdown-content">
                    <a 
                      v-if="canChangeRoles && member.role !== 'admin'"
                      class="dropdown-item"
                      @click="changeRole(member, 'admin')"
                    >
                      <span class="icon">
                        <i class="fas fa-crown"></i>
                      </span>
                      <span>Make Admin</span>
                    </a>
                    <a 
                      v-if="canChangeRoles && member.role !== 'editor'"
                      class="dropdown-item"
                      @click="changeRole(member, 'editor')"
                    >
                      <span class="icon">
                        <i class="fas fa-edit"></i>
                      </span>
                      <span>Make Editor</span>
                    </a>
                    <a 
                      v-if="canChangeRoles && member.role !== 'viewer'"
                      class="dropdown-item"
                      @click="changeRole(member, 'viewer')"
                    >
                      <span class="icon">
                        <i class="fas fa-eye"></i>
                      </span>
                      <span>Make Viewer</span>
                    </a>
                    <hr v-if="canChangeRoles" class="dropdown-divider">
                    <a 
                      class="dropdown-item has-text-danger"
                      @click="removeMember(member)"
                    >
                      <span class="icon">
                        <i class="fas fa-user-minus"></i>
                      </span>
                      <span>Remove Member</span>
                    </a>
                  </div>
                </div>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Confirmation Modal -->
    <div v-if="showConfirmModal" class="modal is-active">
      <div class="modal-background" @click="cancelAction"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">{{ confirmAction.title }}</p>
          <button class="delete" @click="cancelAction"></button>
        </header>
        <section class="modal-card-body">
          <p>{{ confirmAction.message }}</p>
        </section>
        <footer class="modal-card-foot">
          <button 
            class="button is-danger"
            :class="{ 'is-loading': actionLoading }"
            @click="executeAction"
          >
            {{ confirmAction.confirmText }}
          </button>
          <button class="button" @click="cancelAction">Cancel</button>
        </footer>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useGroupsStore } from '../../stores/groups';
import { useAuthStore } from '../../stores/auth';
import { getRoleDisplayName, getRoleColor, getPermissions } from '../../types/group';
import type { GroupWithMembers, GroupMember, UserRole } from '../../types/group';

// Props
const props = defineProps<{
  group: GroupWithMembers;
  userRole: UserRole | null;
}>();

// Emits
const emit = defineEmits<{
  memberRemoved: [];
  roleUpdated: [];
}>();

const groupsStore = useGroupsStore();
const authStore = useAuthStore();

const activeDropdown = ref<string | null>(null);
const showConfirmModal = ref(false);
const actionLoading = ref(false);
const confirmAction = ref<{
  title: string;
  message: string;
  confirmText: string;
  action: () => Promise<void>;
}>({
  title: '',
  message: '',
  confirmText: '',
  action: async () => {}
});

// Computed properties
const permissions = computed(() => {
  return props.userRole ? getPermissions(props.userRole) : null;
});

const canManageMembers = computed(() => {
  return permissions.value?.canRemoveMembers || false;
});

const canChangeRoles = computed(() => {
  return props.userRole === 'admin';
});

// Methods
const getInitials = (name: string): string => {
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
};

const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString();
};

const toggleDropdown = (userId: string) => {
  activeDropdown.value = activeDropdown.value === userId ? null : userId;
};

const changeRole = (member: GroupMember, newRole: UserRole) => {
  activeDropdown.value = null;
  
  confirmAction.value = {
    title: 'Change Member Role',
    message: `Are you sure you want to change ${member.displayName}'s role to ${getRoleDisplayName(newRole)}?`,
    confirmText: 'Change Role',
    action: async () => {
      try {
        await groupsStore.updateMemberRole(props.group.id, member.userId, { role: newRole });
        emit('roleUpdated');
      } catch (error) {
        console.error('Failed to update member role:', error);
        throw error;
      }
    }
  };
  
  showConfirmModal.value = true;
};

const removeMember = (member: GroupMember) => {
  activeDropdown.value = null;
  
  const isCurrentUser = authStore.user?.id === member.userId;
  
  confirmAction.value = {
    title: isCurrentUser ? 'Leave Group' : 'Remove Member',
    message: isCurrentUser 
      ? 'Are you sure you want to leave this group?' 
      : `Are you sure you want to remove ${member.displayName} from this group?`,
    confirmText: isCurrentUser ? 'Leave Group' : 'Remove Member',
    action: async () => {
      try {
        await groupsStore.removeMember(props.group.id, member.userId);
        emit('memberRemoved');
      } catch (error) {
        console.error('Failed to remove member:', error);
        throw error;
      }
    }
  };
  
  showConfirmModal.value = true;
};

const executeAction = async () => {
  try {
    actionLoading.value = true;
    await confirmAction.value.action();
    showConfirmModal.value = false;
  } catch (error) {
    // Error is handled by the store
  } finally {
    actionLoading.value = false;
  }
};

const cancelAction = () => {
  showConfirmModal.value = false;
  activeDropdown.value = null;
};

// Close dropdown when clicking outside
document.addEventListener('click', (event) => {
  const target = event.target as HTMLElement;
  if (!target.closest('.dropdown')) {
    activeDropdown.value = null;
  }
});
</script>

<style scoped>
.dropdown {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  right: 0;
  top: 100%;
  z-index: 20;
}

.media {
  align-items: center;
}

.button.is-ghost {
  background: transparent;
  border: none;
}

.button.is-ghost:hover {
  background-color: rgba(0, 0, 0, 0.05);
}
</style>