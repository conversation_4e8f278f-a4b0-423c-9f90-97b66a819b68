<template>
  <div class="google-signin-container" v-if="GOOGLE_CLIENT_ID && GOOGLE_CLIENT_ID !== 'your-google-client-id-here'">
    <button
      @click="handleGoogleSignIn"
      :disabled="isLoading"
      class="button is-light is-fullwidth google-signin-button"
      type="button"
    >
      <span class="icon">
        <svg width="18" height="18" viewBox="0 0 24 24">
          <path
            fill="#4285F4"
            d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
          />
          <path
            fill="#34A853"
            d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
          />
          <path
            fill="#FBBC05"
            d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
          />
          <path
            fill="#EA4335"
            d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
          />
        </svg>
      </span>
      <span>{{ buttonText }}</span>
    </button>
    
    <!-- Google One Tap will be rendered here -->
    <div id="g_id_onload" ref="googleOneTap"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useAuthStore } from '@/stores/auth'

interface Props {
  buttonText?: string
  mode?: 'signin' | 'signup'
}

const props = withDefaults(defineProps<Props>(), {
  buttonText: 'Continue with Google',
  mode: 'signin'
})

const emit = defineEmits<{
  success: [message: string]
  error: [error: string]
}>()

const authStore = useAuthStore()
const isLoading = ref(false)
const googleOneTap = ref<HTMLElement>()

// Google Identity Services configuration
const GOOGLE_CLIENT_ID = import.meta.env.VITE_GOOGLE_CLIENT_ID

const initializeGoogleSignIn = () => {
  if (!GOOGLE_CLIENT_ID) {
    console.warn('Google Client ID not configured')
    return
  }

  // Load Google Identity Services script
  if (!window.google) {
    const script = document.createElement('script')
    script.src = 'https://accounts.google.com/gsi/client'
    script.async = true
    script.defer = true
    script.onload = setupGoogleSignIn
    document.head.appendChild(script)
  } else {
    setupGoogleSignIn()
  }
}

const setupGoogleSignIn = () => {
  if (!window.google?.accounts?.id) {
    console.error('Google Identity Services not loaded')
    emit('error', 'Google Sign-In services not available')
    return
  }

  try {
    // Initialize Google Sign-In
    window.google.accounts.id.initialize({
      client_id: GOOGLE_CLIENT_ID,
      callback: handleCredentialResponse,
      auto_select: false,
      cancel_on_tap_outside: true,
      context: 'signin'
    })

    // Render One Tap if in signin mode
    if (props.mode === 'signin' && googleOneTap.value) {
      window.google.accounts.id.prompt((notification: any) => {
        if (notification.isNotDisplayed() || notification.isSkippedMoment()) {
          console.log('Google One Tap not displayed:', notification.getNotDisplayedReason())
        }
      })
    }
  } catch (error) {
    console.error('Failed to initialize Google Sign-In:', error)
    emit('error', 'Failed to initialize Google Sign-In')
  }
}

const handleCredentialResponse = async (response: any) => {
  if (!response?.credential) {
    console.error('No credential received from Google')
    emit('error', 'No credential received from Google')
    return
  }

  isLoading.value = true
  
  try {
    const result = await authStore.googleAuth(response.credential)
    
    if (result.success) {
      emit('success', result.message || 'Successfully signed in with Google')
    } else {
      emit('error', result.error || 'Failed to sign in with Google')
    }
  } catch (error) {
    console.error('Google authentication error:', error)
    const errorMessage = error instanceof Error ? error.message : 'Authentication failed'
    emit('error', errorMessage)
  } finally {
    isLoading.value = false
  }
}

const handleGoogleSignIn = () => {
  if (!window.google?.accounts?.id) {
    emit('error', 'Google Sign-In not available')
    return
  }

  // Trigger Google Sign-In popup
  try {
    window.google.accounts.id.prompt()
  } catch (error) {
    console.error('Google Sign-In error:', error)
    emit('error', 'Failed to initiate Google Sign-In')
  }
}

onMounted(() => {
  initializeGoogleSignIn()
})

onUnmounted(() => {
  // Clean up Google One Tap
  if (window.google?.accounts?.id) {
    window.google.accounts.id.cancel()
  }
})

// Declare global types for Google Identity Services
declare global {
  interface Window {
    google?: {
      accounts?: {
        id?: {
          initialize: (config: any) => void
          prompt: (callback?: (notification: any) => void) => void
          cancel: () => void
          renderButton: (element: HTMLElement, config: any) => void
        }
      }
    }
  }
}
</script>

<style scoped>
.google-signin-container {
  position: relative;
}

.google-signin-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border: 1px solid #dadce0;
  background-color: #fff;
  color: #3c4043;
  font-weight: 500;
  transition: all 0.2s ease;
}

.google-signin-button:hover:not(:disabled) {
  background-color: #f8f9fa;
  border-color: #dadce0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.google-signin-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.google-signin-button .icon {
  margin: 0;
}

.google-signin-button .icon svg {
  width: 18px;
  height: 18px;
}
</style>