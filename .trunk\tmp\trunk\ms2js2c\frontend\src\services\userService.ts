import { http } from '../utils/http';

interface UserPreferences {
  theme: 'light' | 'dark' | 'auto'
  language: string
  timezone: string
  autoSaveInterval: number
  notifications: {
    email: boolean
    push: boolean
    mentions: boolean
  }
}

interface UpdateProfileData {
  display_name?: string
  avatar_url?: string
}

interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

class UserService {

  async getSettings(): Promise<ApiResponse<{ preferences: UserPreferences }>> {
    try {
      const response = await http.get<{ preferences: UserPreferences }>('/user/settings')
      
      if (response.error) {
        return {
          success: false,
          error: response.error
        }
      }

      return {
        success: true,
        data: response.data
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch settings'
      }
    }
  }

  async updateSettings(preferences: Partial<UserPreferences>): Promise<ApiResponse<{ preferences: UserPreferences }>> {
    try {
      const response = await http.put<{ preferences: UserPreferences; message: string }>('/user/settings', preferences)
      
      if (response.error) {
        return {
          success: false,
          error: response.error
        }
      }

      return {
        success: true,
        data: { preferences: response.data!.preferences },
        message: response.data!.message
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update settings'
      }
    }
  }

  async updateProfile(profileData: UpdateProfileData): Promise<ApiResponse<{ user: any }>> {
    try {
      const response = await http.put<{ user: any; message: string }>('/user/profile', profileData)
      
      if (response.error) {
        return {
          success: false,
          error: response.error
        }
      }

      return {
        success: true,
        data: { user: response.data!.user },
        message: response.data!.message
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update profile'
      }
    }
  }

  async exportData(): Promise<ApiResponse<Blob>> {
    try {
      const response = await http.get<Blob>('/user/export')
      
      if (response.error) {
        return {
          success: false,
          error: response.error
        }
      }

      return {
        success: true,
        data: response.data!
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to export data'
      }
    }
  }

  downloadExportedData(blob: Blob, filename: string = 'user-data.json') {
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  }
}

export const userService = new UserService()
export type { UserPreferences, UpdateProfileData }