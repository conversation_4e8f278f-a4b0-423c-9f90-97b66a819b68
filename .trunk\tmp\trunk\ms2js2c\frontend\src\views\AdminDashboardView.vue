<template>
  <div class="admin-dashboard">
    <div class="container is-fluid">
      <!-- Header -->
      <div class="level mb-6">
        <div class="level-left">
          <div class="level-item">
            <div>
              <h1 class="title is-3">Admin Dashboard</h1>
              <p class="subtitle is-6">System overview and management</p>
            </div>
          </div>
        </div>
        <div class="level-right">
          <div class="level-item">
            <div class="field is-grouped">
              <div class="control">
                <AdminNotifications />
              </div>
              <div class="control">
                <button 
                  class="button is-primary" 
                  @click="refreshDashboard"
                  :class="{ 'is-loading': isLoading }"
                >
                  <span class="icon">
                    <i class="fas fa-sync-alt"></i>
                  </span>
                  <span>Refresh</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Error Message -->
      <div v-if="error" class="notification is-danger mb-5">
        <button class="delete" @click="error = null"></button>
        {{ error }}
      </div>

      <!-- Loading State -->
      <div v-if="isLoading && !dashboardStats" class="has-text-centered py-6">
        <div class="is-size-4 mb-3">
          <i class="fas fa-spinner fa-spin"></i>
        </div>
        <p>Loading dashboard...</p>
      </div>

      <!-- Dashboard Content -->
      <div v-else-if="dashboardStats">
        <!-- System Health Alert -->
        <div 
          v-if="dashboardStats.systemHealth.status !== 'healthy'" 
          class="notification mb-5"
          :class="{
            'is-warning': dashboardStats.systemHealth.status === 'warning',
            'is-danger': dashboardStats.systemHealth.status === 'critical'
          }"
        >
          <strong>System Health Alert:</strong>
          <span v-if="dashboardStats.systemHealth.status === 'warning'">
            System performance is degraded. Average response time: {{ Math.round(dashboardStats.systemHealth.avg_response_time) }}ms
          </span>
          <span v-else>
            System is experiencing critical issues. Error rate: {{ dashboardStats.systemHealth.error_rate.toFixed(2) }}%
          </span>
        </div>

        <!-- Stats Cards -->
        <div class="columns is-multiline mb-6">
          <!-- User Stats -->
          <div class="column is-3">
            <div class="card">
              <div class="card-content">
                <div class="media">
                  <div class="media-left">
                    <span class="icon is-large has-text-primary">
                      <i class="fas fa-users fa-2x"></i>
                    </span>
                  </div>
                  <div class="media-content">
                    <p class="title is-4">{{ dashboardStats.userStats.total }}</p>
                    <p class="subtitle is-6">Total Users</p>
                  </div>
                </div>
                <div class="content">
                  <div class="level is-mobile">
                    <div class="level-item has-text-centered">
                      <div>
                        <p class="heading">Online</p>
                        <p class="title is-6 has-text-success">
                          <span class="icon is-small">
                            <i class="fas fa-circle"></i>
                          </span>
                          {{ dashboardStats.userStats.online || 0 }}
                        </p>
                      </div>
                    </div>
                    <div class="level-item has-text-centered">
                      <div>
                        <p class="heading">Verified</p>
                        <p class="title is-6">{{ dashboardStats.userStats.verified }}</p>
                      </div>
                    </div>
                    <div class="level-item has-text-centered">
                      <div>
                        <p class="heading">New This Week</p>
                        <p class="title is-6">{{ dashboardStats.userStats.new_this_week }}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Note Stats -->
          <div class="column is-3">
            <div class="card">
              <div class="card-content">
                <div class="media">
                  <div class="media-left">
                    <span class="icon is-large has-text-info">
                      <i class="fas fa-sticky-note fa-2x"></i>
                    </span>
                  </div>
                  <div class="media-content">
                    <p class="title is-4">{{ dashboardStats.noteStats.total }}</p>
                    <p class="subtitle is-6">Total Notes</p>
                  </div>
                </div>
                <div class="content">
                  <div class="level is-mobile">
                    <div class="level-item has-text-centered">
                      <div>
                        <p class="heading">Rich Text</p>
                        <p class="title is-6">{{ dashboardStats.noteStats.richtext }}</p>
                      </div>
                    </div>
                    <div class="level-item has-text-centered">
                      <div>
                        <p class="heading">Markdown</p>
                        <p class="title is-6">{{ dashboardStats.noteStats.markdown }}</p>
                      </div>
                    </div>
                    <div class="level-item has-text-centered">
                      <div>
                        <p class="heading">Kanban</p>
                        <p class="title is-6">{{ dashboardStats.noteStats.kanban }}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Group Stats -->
          <div class="column is-3">
            <div class="card">
              <div class="card-content">
                <div class="media">
                  <div class="media-left">
                    <span class="icon is-large has-text-success">
                      <i class="fas fa-layer-group fa-2x"></i>
                    </span>
                  </div>
                  <div class="media-content">
                    <p class="title is-4">{{ dashboardStats.groupStats.total }}</p>
                    <p class="subtitle is-6">Total Groups</p>
                  </div>
                </div>
                <div class="content">
                  <div class="level is-mobile">
                    <div class="level-item has-text-centered">
                      <div>
                        <p class="heading">Avg Members</p>
                        <p class="title is-6">{{ Math.round(dashboardStats.groupStats.avg_members || 0) }}</p>
                      </div>
                    </div>
                    <div class="level-item has-text-centered">
                      <div>
                        <p class="heading">New This Week</p>
                        <p class="title is-6">{{ dashboardStats.groupStats.created_this_week }}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- System Health -->
          <div class="column is-3">
            <div class="card">
              <div class="card-content">
                <div class="media">
                  <div class="media-left">
                    <span class="icon is-large" :class="{
                      'has-text-success': dashboardStats.systemHealth.status === 'healthy',
                      'has-text-warning': dashboardStats.systemHealth.status === 'warning',
                      'has-text-danger': dashboardStats.systemHealth.status === 'critical'
                    }">
                      <i class="fas fa-heartbeat fa-2x"></i>
                    </span>
                  </div>
                  <div class="media-content">
                    <p class="title is-4 text-capitalize">{{ dashboardStats.systemHealth.status }}</p>
                    <p class="subtitle is-6">System Health</p>
                  </div>
                </div>
                <div class="content">
                  <div class="level is-mobile">
                    <div class="level-item has-text-centered">
                      <div>
                        <p class="heading">Avg Response</p>
                        <p class="title is-6">{{ Math.round(dashboardStats.systemHealth.avg_response_time) }}ms</p>
                      </div>
                    </div>
                    <div class="level-item has-text-centered">
                      <div>
                        <p class="heading">Error Rate</p>
                        <p class="title is-6">{{ dashboardStats.systemHealth.error_rate.toFixed(1) }}%</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Recent Activity -->
        <div class="columns">
          <div class="column is-8">
            <div class="card">
              <header class="card-header">
                <p class="card-header-title">Recent Activity</p>
              </header>
              <div class="card-content">
                <div v-if="dashboardStats.recentActivity.length === 0" class="has-text-centered py-4">
                  <p class="has-text-grey">No recent activity</p>
                </div>
                <div v-else>
                  <div 
                    v-for="activity in dashboardStats.recentActivity.slice(0, 10)" 
                    :key="`${activity.action}-${activity.user_id}-${activity.created_at}`"
                    class="media"
                  >
                    <div class="media-left">
                      <span class="icon">
                        <i class="fas" :class="{
                          'fa-user': activity.resource_type === 'user',
                          'fa-sticky-note': activity.resource_type === 'note',
                          'fa-layer-group': activity.resource_type === 'group',
                          'fa-search': activity.resource_type === 'search',
                          'fa-cog': activity.resource_type === 'system'
                        }"></i>
                      </span>
                    </div>
                    <div class="media-content">
                      <div class="content">
                        <p>
                          <strong>{{ formatAction(activity.action) }}</strong>
                          <small class="has-text-grey">{{ activity.resource_type }}</small>
                          <br>
                          <small class="has-text-grey">
                            {{ formatDate(activity.created_at) }} • {{ activity.count }} times
                          </small>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="column is-4">
            <div class="card">
              <header class="card-header">
                <p class="card-header-title">Quick Actions</p>
              </header>
              <div class="card-content">
                <div class="buttons">
                  <router-link to="/admin/users" class="button is-primary is-fullwidth">
                    <span class="icon">
                      <i class="fas fa-users"></i>
                    </span>
                    <span>Manage Users</span>
                  </router-link>
                  
                  <router-link to="/admin/reports" class="button is-warning is-fullwidth">
                    <span class="icon">
                      <i class="fas fa-flag"></i>
                    </span>
                    <span>Content Reports</span>
                  </router-link>
                  
                  <router-link to="/admin/system" class="button is-info is-fullwidth">
                    <span class="icon">
                      <i class="fas fa-cogs"></i>
                    </span>
                    <span>System Settings</span>
                  </router-link>
                  
                  <router-link to="/admin/metrics" class="button is-link is-fullwidth">
                    <span class="icon">
                      <i class="fas fa-chart-line"></i>
                    </span>
                    <span>Performance Metrics</span>
                  </router-link>
                  
                  <router-link to="/admin/performance" class="button is-success is-fullwidth">
                    <span class="icon">
                      <i class="fas fa-tachometer-alt"></i>
                    </span>
                    <span>Performance Monitor</span>
                  </router-link>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Admin Activity Log -->
        <div class="columns mt-5">
          <div class="column">
            <AdminAuditTrail :show-filters="false" :limit="10" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useAdminStore } from '../stores/admin'
import { storeToRefs } from 'pinia'
import AdminNotifications from '../components/admin/AdminNotifications.vue'
import AdminAuditTrail from '../components/admin/AdminAuditTrail.vue'

const adminStore = useAdminStore()
const { dashboardStats, isLoading, error } = storeToRefs(adminStore)

onMounted(() => {
  adminStore.loadDashboard()
})

const refreshDashboard = () => {
  adminStore.loadDashboard()
}

const formatAction = (action: string) => {
  return action.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMins = Math.floor(diffMs / 60000)
  const diffHours = Math.floor(diffMins / 60)
  const diffDays = Math.floor(diffHours / 24)

  if (diffMins < 1) return 'Just now'
  if (diffMins < 60) return `${diffMins}m ago`
  if (diffHours < 24) return `${diffHours}h ago`
  if (diffDays < 7) return `${diffDays}d ago`
  
  return date.toLocaleDateString()
}
</script>

<style scoped>
.admin-dashboard {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 2rem 0;
}

.card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.card-content {
  padding: 1.5rem;
}

.media + .media {
  border-top: 1px solid #dbdbdb;
  margin-top: 1rem;
  padding-top: 1rem;
}

.text-capitalize {
  text-transform: capitalize;
}

.level.is-mobile {
  margin-bottom: 0;
}

.level-item {
  flex-shrink: 1;
}

.buttons .button {
  margin-bottom: 0.5rem;
}
</style>