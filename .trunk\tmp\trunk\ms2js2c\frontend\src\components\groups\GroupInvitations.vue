<template>
  <div class="group-invitations">
    <div class="level">
      <div class="level-left">
        <div class="level-item">
          <h3 class="title is-5">Pending Invitations</h3>
        </div>
      </div>
      <div class="level-right">
        <div class="level-item">
          <button 
            class="button is-primary"
            @click="$emit('invitation-sent')"
          >
            <span class="icon">
              <i class="fas fa-user-plus"></i>
            </span>
            <span>Send Invitation</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Loading state -->
    <div v-if="loading" class="has-text-centered py-4">
      <div class="loader"></div>
      <p class="mt-2">Loading invitations...</p>
    </div>

    <!-- Error state -->
    <div v-else-if="error" class="notification is-danger">
      <button class="delete" @click="clearError"></button>
      {{ error }}
    </div>

    <!-- Empty state -->
    <div v-else-if="invitations.length === 0" class="has-text-centered py-6">
      <div class="icon is-large has-text-grey-light mb-4">
        <i class="fas fa-envelope fa-2x"></i>
      </div>
      <h4 class="title is-6 has-text-grey">No pending invitations</h4>
      <p class="has-text-grey">
        All sent invitations have been accepted or have expired.
      </p>
    </div>

    <!-- Invitations table -->
    <div v-else class="table-container">
      <table class="table is-fullwidth is-hoverable">
        <thead>
          <tr>
            <th>Email</th>
            <th>Role</th>
            <th>Expires</th>
            <th>Sent</th>
            <th class="has-text-right">Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="invitation in invitations" :key="invitation.id">
            <td>
              <strong>{{ invitation.email }}</strong>
            </td>
            <td>
              <span 
                class="tag"
                :class="getRoleColor(invitation.role)"
              >
                {{ getRoleDisplayName(invitation.role) }}
              </span>
            </td>
            <td>
              <span 
                class="has-text-grey"
                :class="{ 'has-text-danger': isExpiringSoon(invitation.expiresAt) }"
              >
                {{ formatDate(invitation.expiresAt) }}
              </span>
              <span 
                v-if="isExpiringSoon(invitation.expiresAt)"
                class="tag is-warning is-small ml-2"
              >
                Expiring Soon
              </span>
            </td>
            <td>
              <span class="has-text-grey">
                {{ formatDate(invitation.createdAt) }}
              </span>
            </td>
            <td class="has-text-right">
              <div class="buttons is-right">
                <button 
                  class="button is-small is-light"
                  @click="copyInvitationLink(invitation)"
                  title="Copy invitation link"
                >
                  <span class="icon">
                    <i class="fas fa-copy"></i>
                  </span>
                </button>
                <button 
                  class="button is-small is-light"
                  @click="resendInvitation(invitation)"
                  title="Resend invitation"
                >
                  <span class="icon">
                    <i class="fas fa-paper-plane"></i>
                  </span>
                </button>
                <button 
                  class="button is-small is-danger is-light"
                  @click="cancelInvitation(invitation)"
                  title="Cancel invitation"
                >
                  <span class="icon">
                    <i class="fas fa-times"></i>
                  </span>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Success notification -->
    <div v-if="successMessage" class="notification is-success">
      <button class="delete" @click="successMessage = ''"></button>
      {{ successMessage }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useGroupsStore } from '../../stores/groups';
import { getRoleDisplayName, getRoleColor } from '../../types/group';
import type { GroupWithMembers, GroupInvitation, UserRole } from '../../types/group';

// Props
const props = defineProps<{
  group: GroupWithMembers;
}>();

// Emits
const emit = defineEmits<{
  'invitation-sent': [];
}>();

const groupsStore = useGroupsStore();

const successMessage = ref('');

// Computed properties
const invitations = computed(() => groupsStore.groupInvitations);
const loading = computed(() => groupsStore.loading);
const error = computed(() => groupsStore.error);

// Methods
const clearError = () => {
  groupsStore.clearError();
};

const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString();
};

const isExpiringSoon = (expiresAt: string): boolean => {
  const expiryDate = new Date(expiresAt);
  const now = new Date();
  const daysDiff = (expiryDate.getTime() - now.getTime()) / (1000 * 3600 * 24);
  return daysDiff <= 2; // Expires within 2 days
};

const copyInvitationLink = async (invitation: GroupInvitation) => {
  try {
    // In a real implementation, this would be the actual invitation URL
    const invitationUrl = `${window.location.origin}/groups/invite/${invitation.id}`;
    await navigator.clipboard.writeText(invitationUrl);
    successMessage.value = 'Invitation link copied to clipboard!';
    setTimeout(() => {
      successMessage.value = '';
    }, 3000);
  } catch (error) {
    console.error('Failed to copy invitation link:', error);
  }
};

const resendInvitation = async (invitation: GroupInvitation) => {
  try {
    // In a real implementation, this would call an API to resend the invitation email
    console.log('Resending invitation to:', invitation.email);
    successMessage.value = `Invitation resent to ${invitation.email}`;
    setTimeout(() => {
      successMessage.value = '';
    }, 3000);
  } catch (error) {
    console.error('Failed to resend invitation:', error);
  }
};

const cancelInvitation = async (invitation: GroupInvitation) => {
  if (!confirm(`Are you sure you want to cancel the invitation for ${invitation.email}?`)) {
    return;
  }

  try {
    // In a real implementation, this would call an API to cancel the invitation
    console.log('Canceling invitation for:', invitation.email);
    
    // Remove from local list (in real implementation, this would be handled by the API)
    const index = invitations.value.findIndex(inv => inv.id === invitation.id);
    if (index !== -1) {
      groupsStore.groupInvitations.splice(index, 1);
    }
    
    successMessage.value = `Invitation for ${invitation.email} has been canceled`;
    setTimeout(() => {
      successMessage.value = '';
    }, 3000);
  } catch (error) {
    console.error('Failed to cancel invitation:', error);
  }
};

// Lifecycle
onMounted(async () => {
  try {
    await groupsStore.loadGroupInvitations(props.group.id);
  } catch (error) {
    console.error('Failed to load group invitations:', error);
  }
});
</script>

<style scoped>
.loader {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 2s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.buttons.is-right {
  justify-content: flex-end;
}
</style>