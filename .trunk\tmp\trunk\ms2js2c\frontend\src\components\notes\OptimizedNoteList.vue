<template>
  <div class="optimized-note-list">
    <!-- Search and Filter Bar -->
    <div class="note-list-controls mb-4">
      <div class="field has-addons">
        <div class="control has-icons-left is-expanded">
          <input
            v-model="searchQuery"
            class="input"
            type="text"
            placeholder="Search notes..."
            @input="debouncedSearch"
          >
          <span class="icon is-left">
            <i class="fas fa-search"></i>
          </span>
        </div>
        <div class="control">
          <div class="select">
            <select v-model="selectedType" @change="applyFilters">
              <option value="">All Types</option>
              <option value="richtext">Rich Text</option>
              <option value="markdown">Markdown</option>
              <option value="kanban">Kanban</option>
            </select>
          </div>
        </div>
        <div class="control">
          <div class="select">
            <select v-model="sortBy" @change="applySorting">
              <option value="updated_at">Last Modified</option>
              <option value="created_at">Created Date</option>
              <option value="title">Title</option>
            </select>
          </div>
        </div>
      </div>
    </div>

    <!-- Performance Stats -->
    <div v-if="showPerformanceStats" class="performance-stats mb-4">
      <div class="tags">
        <span class="tag is-info">
          <i class="fas fa-list mr-1"></i>
          {{ filteredNotes.length }} notes
        </span>
        <span class="tag is-success">
          <i class="fas fa-clock mr-1"></i>
          {{ lastRenderTime.toFixed(2) }}ms render
        </span>
        <span class="tag" :class="cacheHitRate > 80 ? 'is-success' : 'is-warning'">
          <i class="fas fa-database mr-1"></i>
          {{ cacheHitRate.toFixed(1) }}% cache hit
        </span>
      </div>
    </div>

    <!-- Virtual Scrolled Note List -->
    <VirtualScrollList
      ref="virtualListRef"
      :items="filteredNotes"
      :item-height="noteItemHeight"
      :container-height="containerHeight"
      :buffer="bufferSize"
      :load-more="loadMoreNotes"
      :has-more="hasMoreNotes"
      :is-loading="isLoadingMore"
      @scroll="handleScroll"
      @load-more="handleLoadMore"
    >
      <template #default="{ item: note, index }">
        <div
          class="note-item"
          :class="{ 'is-selected': selectedNoteId === note.id }"
          @click="selectNote(note)"
          @dblclick="openNote(note)"
        >
          <div class="note-item-content">
            <!-- Note Icon -->
            <div class="note-icon">
              <i class="fas" :class="getNoteIcon(note.note_type)"></i>
            </div>

            <!-- Note Details -->
            <div class="note-details">
              <h4 class="note-title">{{ note.title || 'Untitled' }}</h4>
              <p class="note-preview">{{ getPreviewText(note.content) }}</p>
              
              <!-- Note Meta -->
              <div class="note-meta">
                <span class="note-date">
                  <i class="fas fa-clock"></i>
                  {{ formatDate(note.updated_at) }}
                </span>
                <span v-if="note.tags && note.tags.length" class="note-tags">
                  <i class="fas fa-tags"></i>
                  {{ note.tags.slice(0, 3).join(', ') }}
                  <span v-if="note.tags.length > 3">+{{ note.tags.length - 3 }}</span>
                </span>
                <span v-if="note.group_name" class="note-group">
                  <i class="fas fa-users"></i>
                  {{ note.group_name }}
                </span>
              </div>
            </div>

            <!-- Note Actions -->
            <div class="note-actions">
              <div class="dropdown is-hoverable is-right">
                <div class="dropdown-trigger">
                  <button class="button is-small is-ghost" @click.stop>
                    <i class="fas fa-ellipsis-v"></i>
                  </button>
                </div>
                <div class="dropdown-menu">
                  <div class="dropdown-content">
                    <a class="dropdown-item" @click.stop="openNote(note)">
                      <i class="fas fa-eye mr-2"></i>
                      Open
                    </a>
                    <a class="dropdown-item" @click.stop="editNote(note)">
                      <i class="fas fa-edit mr-2"></i>
                      Edit
                    </a>
                    <a class="dropdown-item" @click.stop="shareNote(note)">
                      <i class="fas fa-share mr-2"></i>
                      Share
                    </a>
                    <hr class="dropdown-divider">
                    <a class="dropdown-item has-text-danger" @click.stop="deleteNote(note)">
                      <i class="fas fa-trash mr-2"></i>
                      Delete
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </VirtualScrollList>

    <!-- Empty State -->
    <div v-if="filteredNotes.length === 0 && !isLoading" class="empty-state">
      <div class="has-text-centered py-6">
        <i class="fas fa-sticky-note fa-3x has-text-grey-light mb-4"></i>
        <h3 class="title is-5 has-text-grey">No notes found</h3>
        <p class="has-text-grey">
          {{ searchQuery ? 'Try adjusting your search terms' : 'Create your first note to get started' }}
        </p>
        <button v-if="!searchQuery" class="button is-primary mt-4" @click="createNote">
          <i class="fas fa-plus mr-2"></i>
          Create Note
        </button>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="loading-state">
      <div class="has-text-centered py-6">
        <div class="button is-loading is-large is-ghost"></div>
        <p class="mt-4">Loading notes...</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import VirtualScrollList from '../ui/VirtualScrollList.vue'
import { usePerformance } from '../../composables/usePerformance'
import { searchService } from '../../services/searchService'
import { cacheService } from '../../services/cacheService'
import { noteService } from '../../services/noteService'

interface Note {
  id: string
  title: string
  content: string
  note_type: 'richtext' | 'markdown' | 'kanban'
  tags?: string[]
  group_name?: string
  created_at: string
  updated_at: string
  is_archived: boolean
}

interface Props {
  containerHeight?: number
  showPerformanceStats?: boolean
  enableVirtualScrolling?: boolean
  pageSize?: number
}

const props = withDefaults(defineProps<Props>(), {
  containerHeight: 600,
  showPerformanceStats: false,
  enableVirtualScrolling: true,
  pageSize: 50
})

const emit = defineEmits<{
  noteSelected: [note: Note]
  noteOpened: [note: Note]
  noteDeleted: [noteId: string]
}>()

const router = useRouter()

// Performance monitoring
const { metrics, budgets } = usePerformance({})

// Lightweight tracking shims (no-op timers)
const trackInteraction = (action: string, area?: string) => {
  const start = performance.now()
  return () => {
    const _ = [action, area, start]
    void _
  }
}
const trackApiCall = async <T>(fn: () => Promise<T>) => fn()

// Reactive state
const notes = ref<Note[]>([])
const searchQuery = ref('')
const selectedType = ref('')
const sortBy = ref('updated_at')
const selectedNoteId = ref<string | null>(null)
const isLoading = ref(false)
const isLoadingMore = ref(false)
const hasMoreNotes = ref(true)
const currentPage = ref(1)
const lastRenderTime = ref(0)
const cacheHitRate = ref(0)

// Virtual scrolling settings
const noteItemHeight = 120
const bufferSize = 5
const virtualListRef = ref()

// Computed properties
const filteredNotes = computed(() => {
  const startTime = performance.now()
  
  let filtered = notes.value

  // Apply search filter
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(note => 
      note.title.toLowerCase().includes(query) ||
      note.content.toLowerCase().includes(query) ||
      (note.tags && note.tags.some(tag => tag.toLowerCase().includes(query)))
    )
  }

  // Apply type filter
  if (selectedType.value) {
    filtered = filtered.filter(note => note.note_type === selectedType.value)
  }

  // Apply sorting
  filtered.sort((a, b) => {
    switch (sortBy.value) {
      case 'title':
        return a.title.localeCompare(b.title)
      case 'created_at':
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      case 'updated_at':
      default:
        return new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime()
    }
  })

  lastRenderTime.value = performance.now() - startTime
  return filtered
})

// Methods
const loadNotes = async (page = 1, append = false) => {
  const cacheKey = `notes-page-${page}-${selectedType.value}-${sortBy.value}`
  
  try {
    isLoading.value = !append
    isLoadingMore.value = append

    const result = await trackApiCall(() => noteService.getNotes(
      {
        noteType: (selectedType.value || undefined) as any,
        search: searchQuery.value || undefined
      },
      { page, limit: props.pageSize, sortBy: sortBy.value as any }
    ))

    const toLocal = (n: import('../../services/noteService').Note) => ({
      id: n.id,
      title: n.title,
      content: n.content,
      note_type: n.noteType,
      tags: n.tags?.map(t => t.name),
      created_at: n.createdAt,
      updated_at: n.updatedAt,
      is_archived: n.isArchived
    })

    if (append) {
      notes.value.push(...result.notes.map(toLocal))
    } else {
      notes.value = result.notes.map(toLocal)
    }

    hasMoreNotes.value = result.notes.length === props.pageSize
    currentPage.value = page

    // Update cache hit rate
    updateCacheHitRate()
  } catch (error) {
    console.error('Failed to load notes:', error)
  } finally {
    isLoading.value = false
    isLoadingMore.value = false
  }
}

const loadMoreNotes = async () => {
  if (!hasMoreNotes.value || isLoadingMore.value) return
  await loadNotes(currentPage.value + 1, true)
}

const searchNotes = async () => {
  if (!searchQuery.value.trim()) {
    await loadNotes(1)
    return
  }

  const cacheKey = `search-${searchQuery.value}-${selectedType.value}`
  
  try {
    isLoading.value = true

    const response = await trackApiCall(() => searchService.searchNotes({
      query: searchQuery.value,
      noteType: (selectedType.value || undefined) as any
    }, { page: 1, limit: props.pageSize }))

    notes.value = response.results.map(r => ({
      id: r.note.id,
      title: r.note.title,
      content: r.note.content,
      note_type: r.note.noteType,
      tags: r.note.tags?.map(t => t.name),
      created_at: r.note.createdAt,
      updated_at: r.note.updatedAt,
      is_archived: r.note.isArchived
    }))
    hasMoreNotes.value = false
    updateCacheHitRate()
  } catch (error) {
    console.error('Failed to search notes:', error)
  } finally {
    isLoading.value = false
  }
}

const applyFilters = () => {
  currentPage.value = 1
  if (searchQuery.value.trim()) {
    searchNotes()
  } else {
    loadNotes(1)
  }
}

const applySorting = () => {
  // Invalidate cache when sorting changes
  cacheService.invalidateByTags(['notes'])
  applyFilters()
}

// Debounced search
let searchTimeout: number | null = null
const debouncedSearch = () => {
  if (searchTimeout) clearTimeout(searchTimeout)
  searchTimeout = window.setTimeout(searchNotes, 300)
}

// Note actions
const selectNote = (note: Note) => {
  const endTracking = trackInteraction('select-note', 'note-item')
  selectedNoteId.value = note.id
  emit('noteSelected', note)
  endTracking()
}

const openNote = (note: Note) => {
  const endTracking = trackInteraction('open-note', 'note-item')
  emit('noteOpened', note)
  router.push(`/dashboard/notes/${note.id}`)
  endTracking()
}

const editNote = (note: Note) => {
  const endTracking = trackInteraction('edit-note', 'note-actions')
  router.push(`/dashboard/notes/${note.id}?mode=edit`)
  endTracking()
}

const shareNote = (note: Note) => {
  const endTracking = trackInteraction('share-note', 'note-actions')
  // Implement share functionality
  console.log('Share note:', note.id)
  endTracking()
}

const deleteNote = async (note: Note) => {
  if (!confirm(`Are you sure you want to delete "${note.title}"?`)) return

  const endTracking = trackInteraction('delete-note', 'note-actions')
  
  try {
    await trackApiCall(() => noteService.deleteNote(note.id))

    notes.value = notes.value.filter(n => n.id !== note.id)
    cacheService.invalidateByTags(['notes', `note-${note.id}`])
    emit('noteDeleted', note.id)
  } catch (error) {
    console.error('Failed to delete note:', error)
  } finally {
    endTracking()
  }
}

const createNote = () => {
  const endTracking = trackInteraction('create-note', 'empty-state')
  router.push('/dashboard/notes/new')
  endTracking()
}

// Utility methods
const getNoteIcon = (type: string) => {
  switch (type) {
    case 'markdown': return 'fa-markdown'
    case 'kanban': return 'fa-columns'
    case 'richtext':
    default: return 'fa-file-alt'
  }
}

const getPreviewText = (content: string) => {
  if (!content) return 'No content'
  
  // Strip HTML/Markdown and get first 100 characters
  const text = content.replace(/<[^>]*>/g, '').replace(/[#*_`]/g, '')
  return text.length > 100 ? text.substring(0, 100) + '...' : text
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  if (diffDays === 0) {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  } else if (diffDays === 1) {
    return 'Yesterday'
  } else if (diffDays < 7) {
    return `${diffDays} days ago`
  } else {
    return date.toLocaleDateString()
  }
}

const updateCacheHitRate = () => {
  const stats = cacheService.getStats()
  cacheHitRate.value = stats.hitRate || 0
}

// Event handlers
const handleScroll = (scrollData: { scrollTop: number; scrollLeft: number }) => {
  // Could implement scroll-based features here
}

const handleLoadMore = () => {
  loadMoreNotes()
}

// Lifecycle
onMounted(() => {
  loadNotes(1)
})

onUnmounted(() => {
  if (searchTimeout) clearTimeout(searchTimeout)
})

// Watch for external changes
watch(() => props.pageSize, () => {
  currentPage.value = 1
  loadNotes(1)
})
</script>

<style scoped>
.optimized-note-list {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.note-list-controls {
  flex-shrink: 0;
}

.performance-stats {
  flex-shrink: 0;
}

.note-item {
  padding: 1rem;
  border-bottom: 1px solid #e0e0e0;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.note-item:hover {
  background-color: #f8f9fa;
}

.note-item.is-selected {
  background-color: #e3f2fd;
  border-left: 4px solid #2196f3;
}

.note-item-content {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.note-icon {
  flex-shrink: 0;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  border-radius: 50%;
  color: #666;
}

.note-details {
  flex: 1;
  min-width: 0;
}

.note-title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.note-preview {
  font-size: 0.875rem;
  color: #666;
  margin-bottom: 0.5rem;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.note-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.75rem;
  color: #888;
}

.note-meta span {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.note-actions {
  flex-shrink: 0;
}

.empty-state,
.loading-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Responsive design */
@media (max-width: 768px) {
  .note-item-content {
    gap: 0.5rem;
  }
  
  .note-meta {
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .note-list-controls .field.has-addons {
    flex-direction: column;
  }
  
  .note-list-controls .control {
    width: 100%;
  }
}
</style>