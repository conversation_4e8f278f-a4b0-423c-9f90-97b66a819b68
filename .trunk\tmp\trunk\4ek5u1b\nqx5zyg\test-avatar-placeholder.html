<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Avatar Placeholder Test</title>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    />
    <style>
      body {
        font-family: Arial, sans-serif;
        padding: 20px;
        background: #f5f5f5;
      }

      .test-container {
        display: flex;
        gap: 20px;
        align-items: center;
        margin-bottom: 20px;
      }

      .image {
        display: block;
        position: relative;
        overflow: hidden;
      }

      .image.is-32x32 {
        width: 32px;
        height: 32px;
      }

      .image img {
        display: block;
        height: auto;
        width: 100%;
        object-fit: cover;
      }

      .is-rounded img {
        border-radius: 50%;
      }

      .avatar-placeholder {
        display: flex !important;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        background: #f0f0f0;
        border: 2px solid #ddd;
        border-radius: 50%;
        color: #666;
        transition: all 0.2s ease;
      }

      .avatar-placeholder:hover {
        background: #e8e8e8;
        border-color: #ccc;
      }

      .icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 1.5rem;
        height: 1.5rem;
        flex-shrink: 0;
        color: currentColor;
      }

      .icon.is-large {
        width: 3rem;
        height: 3rem;
      }
    </style>
  </head>
  <body>
    <h1>Avatar Placeholder Test</h1>

    <div class="test-container">
      <h3>With Profile Picture:</h3>
      <figure class="image is-32x32">
        <img
          class="is-rounded"
          src="https://via.placeholder.com/32x32/4285f4/ffffff?text=U"
          alt="User Avatar"
        />
      </figure>
      <span>John Doe</span>
    </div>

    <div class="test-container">
      <h3>Without Profile Picture (Font Awesome Icon):</h3>
      <figure class="image is-32x32">
        <span class="icon is-large avatar-placeholder">
          <i class="fas fa-user fa-lg"></i>
        </span>
      </figure>
      <span>Jane Smith</span>
    </div>

    <div class="test-container">
      <h3>Larger Version:</h3>
      <figure class="image" style="width: 64px; height: 64px">
        <span class="icon is-large avatar-placeholder">
          <i class="fas fa-user fa-2x"></i>
        </span>
      </figure>
      <span>No Profile Picture User</span>
    </div>
  </body>
</html>
