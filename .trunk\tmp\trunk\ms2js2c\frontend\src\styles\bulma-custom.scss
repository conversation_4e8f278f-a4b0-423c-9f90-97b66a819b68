// Custom Bulma build - simplified approach
// Import the main Bulma file and then customize as needed

// Custom overrides can be added here if needed
// Using modular Bulma v1 `@use` API for tree-shaking and correct ordering

// Import Bulma's utilities and base styles (Bulma v1 uses the module system)
@use "bulma/sass/utilities" as *;
@use "bulma/sass/base" as *;

// Import only the components we actually use
@use "bulma/sass/elements/button";
@use "bulma/sass/elements/content";
@use "bulma/sass/elements/icon";
@use "bulma/sass/elements/notification";
@use "bulma/sass/elements/progress";
@use "bulma/sass/elements/table";
@use "bulma/sass/elements/tag";
@use "bulma/sass/elements/title";

// Form elements
@use "bulma/sass/form/shared";
@use "bulma/sass/form/input-textarea";
@use "bulma/sass/form/checkbox-radio";
@use "bulma/sass/form/select";
@use "bulma/sass/form/file";

// Components we use
@use "bulma/sass/components/breadcrumb";
@use "bulma/sass/components/card";
@use "bulma/sass/components/dropdown";
@use "bulma/sass/components/menu";
@use "bulma/sass/components/message";
@use "bulma/sass/components/modal";
@use "bulma/sass/components/navbar";
@use "bulma/sass/components/pagination";
@use "bulma/sass/components/panel";
@use "bulma/sass/components/tabs";

// Layout components
@use "bulma/sass/layout/container";
@use "bulma/sass/layout/level";
@use "bulma/sass/layout/section";

// Grid system
@use "bulma/sass/grid/columns";

// Skip unused components to reduce bundle size:
// - breadcrumb (if not used)
// - carousel (not used)
// - hero (not used) 
// - tile (not used)
// - footer (if not used)

// Custom variables for performance optimization
$navbar-breakpoint: 1024px; // Reduce breakpoint calculations
$tablet: 769px;
$desktop: 1024px;
$widescreen: 1216px;
$fullhd: 1408px;

// Reduce color variations to minimize CSS (Bulma v1 colors map uses single values)
$colors: (
  "primary": $primary,
  "link": $link,
  "info": $info,
  "success": $success,
  "warning": $warning,
  "danger": $danger,
  "dark": $dark,
  "text": $text
);

// Reduce size variations
$sizes: $size-1 $size-2 $size-3 $size-4 $size-5 $size-6;
