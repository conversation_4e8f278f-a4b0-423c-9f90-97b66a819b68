import { v4 as uuidv4 } from 'uuid';

export interface NoteMetadata {
  wordCount?: number;
  readingTime?: number;
  lastEditedBy?: string;
  collaborators?: string[];
}

export interface ShareSettings {
  accessLevel: 'private' | 'shared' | 'unlisted' | 'public';
  permissions: string[];
  expiresAt?: Date;
  passwordProtected: boolean;
}

export interface Note {
  id: string;
  userId: string;
  groupId?: string;
  title: string;
  content: string;
  noteType: 'richtext' | 'markdown' | 'kanban';
  metadata: NoteMetadata;
  isArchived: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface NoteVersion {
  id: string;
  noteId: string;
  content: string;
  versionNumber: number;
  createdAt: Date;
  createdBy: string;
}

export interface Tag {
  id: string;
  name: string;
  userId: string;
  createdAt: Date;
}

export interface CreateNoteData {
  userId: string;
  groupId?: string;
  title: string;
  content: string;
  noteType: 'richtext' | 'markdown' | 'kanban';
  metadata?: NoteMetadata;
}

export interface UpdateNoteData {
  title?: string;
  content?: string;
  metadata?: NoteMetadata;
  isArchived?: boolean;
}

export interface NoteFilters {
  userId: string;
  groupId?: string;
  noteType?: 'richtext' | 'markdown' | 'kanban';
  tags?: string[];
  isArchived?: boolean;
  search?: string;
}

export interface PaginationOptions {
  page: number;
  limit: number;
  sortBy?: 'created_at' | 'updated_at' | 'title';
  sortOrder?: 'asc' | 'desc';
}

export class NoteModel {
  static generateId(): string {
    return uuidv4();
  }

  static validateNoteType(noteType: string): boolean {
    return ['richtext', 'markdown', 'kanban'].includes(noteType);
  }

  static validateTitle(title: string): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (!title || title.trim().length === 0) {
      errors.push('Title is required');
    }
    
    if (title.length > 255) {
      errors.push('Title must be less than 255 characters');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  static validateContent(content: string, noteType: string): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (content === undefined || content === null) {
      errors.push('Content is required');
    }

    // Content length validation (adjust as needed)
    if (content && content.length > 10000000) { // 10MB limit
      errors.push('Content is too large (maximum 10MB)');
    }

    // Type-specific validation
    if (noteType === 'kanban' && content) {
      try {
        const parsed = JSON.parse(content);
        if (!parsed.columns || !Array.isArray(parsed.columns)) {
          errors.push('Kanban content must have a columns array');
        } else {
          // Validate each column
          for (const column of parsed.columns) {
            if (!column.id) {
              errors.push('Each Kanban column must have an id');
            }
            if (!column.title) {
              errors.push('Each Kanban column must have a title');
            }
            if (column.cards && !Array.isArray(column.cards)) {
              errors.push('Kanban column cards must be an array');
            } else if (column.cards) {
              // Validate each card
              for (const card of column.cards) {
                if (!card.id) {
                  errors.push('Each Kanban card must have an id');
                }
                if (card.title === undefined) {
                  errors.push('Each Kanban card must have a title');
                }
              }
            }
          }
        }
      } catch (e) {
        errors.push('Kanban content must be valid JSON');
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  static calculateWordCount(content: string, noteType: string): number {
    if (noteType === 'kanban') {
      try {
        const parsed = JSON.parse(content);
        let wordCount = 0;
        
        // Count words in kanban cards
        if (parsed.columns && Array.isArray(parsed.columns)) {
          parsed.columns.forEach((column: any) => {
            if (column.title) {
              wordCount += column.title.split(/\s+/).filter((word: string) => word.length > 0).length;
            }
            if (column.cards && Array.isArray(column.cards)) {
              column.cards.forEach((card: any) => {
                if (card.title) {
                  wordCount += card.title.split(/\s+/).filter((word: string) => word.length > 0).length;
                }
                if (card.description) {
                  wordCount += card.description.split(/\s+/).filter((word: string) => word.length > 0).length;
                }
              });
            }
          });
        }
        
        return wordCount;
      } catch (e) {
        return 0;
      }
    }

    // For richtext and markdown, strip HTML/markdown and count words
    const textContent = content
      .replace(/<[^>]*>/g, '') // Remove HTML tags
      .replace(/[#*_`~\[\]()]/g, '') // Remove markdown formatting
      .trim();
    
    if (!textContent) return 0;
    
    return textContent.split(/\s+/).filter(word => word.length > 0).length;
  }

  static calculateReadingTime(wordCount: number): number {
    // Average reading speed: 200 words per minute
    const wordsPerMinute = 200;
    return Math.ceil(wordCount / wordsPerMinute);
  }

  static getDefaultMetadata(): NoteMetadata {
    return {
      wordCount: 0,
      readingTime: 0,
      collaborators: []
    };
  }

  static updateMetadata(content: string, noteType: string, existingMetadata: NoteMetadata = {}): NoteMetadata {
    const wordCount = this.calculateWordCount(content, noteType);
    const readingTime = this.calculateReadingTime(wordCount);

    return {
      ...existingMetadata,
      wordCount,
      readingTime
    };
  }
}