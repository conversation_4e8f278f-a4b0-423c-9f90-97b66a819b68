<template>
  <div class="sidebar" :class="{ 'is-collapsed': isCollapsed }">
    <!-- Header -->
    <div class="sidebar-header">
      <div v-if="!isCollapsed" class="sidebar-brand">
        <h1 class="title is-5">Notes</h1>
      </div>
      <button 
        class="button is-ghost sidebar-toggle is-hidden-mobile"
        @click="$emit('toggle-collapse')"
        :title="isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'"
      >
        <span class="icon">
          <i :class="isCollapsed ? 'fas fa-chevron-right' : 'fas fa-chevron-left'"></i>
        </span>
      </button>
      <button 
        class="button is-ghost sidebar-close is-hidden-tablet"
        @click="$emit('close-mobile')"
        title="Close sidebar"
      >
        <span class="icon">
          <i class="fas fa-times"></i>
        </span>
      </button>
    </div>

    <!-- User Profile Section -->
    <div class="sidebar-section user-section">
      <div class="user-profile" :class="{ 'is-collapsed': isCollapsed }">
        <div class="user-avatar">
          <figure class="image is-32x32">
            <img 
              class="is-rounded" 
              :src="authStore.user?.avatarUrl || '/default-avatar.png'" 
              :alt="authStore.user?.displayName || 'User'"
            >
          </figure>
        </div>
        <div v-if="!isCollapsed" class="user-info">
          <p class="user-name">{{ authStore.user?.displayName || 'User' }}</p>
          <p class="user-email">{{ authStore.user?.email }}</p>
        </div>
        <div v-if="!isCollapsed" class="user-actions">
          <div class="dropdown is-hoverable is-right">
            <div class="dropdown-trigger">
              <button class="button is-ghost is-small">
                <span class="icon">
                  <i class="fas fa-ellipsis-v"></i>
                </span>
              </button>
            </div>
            <div class="dropdown-menu">
              <div class="dropdown-content">
                <a class="dropdown-item" @click="openSettings">
                  <span class="icon">
                    <i class="fas fa-cog"></i>
                  </span>
                  <span>Settings</span>
                </a>
                <router-link 
                  v-if="authStore.isAdmin" 
                  to="/admin" 
                  class="dropdown-item"
                >
                  <span class="icon">
                    <i class="fas fa-shield-alt"></i>
                  </span>
                  <span>Admin Panel</span>
                </router-link>
                <hr class="dropdown-divider">
                <a class="dropdown-item" @click="handleLogout">
                  <span class="icon">
                    <i class="fas fa-sign-out-alt"></i>
                  </span>
                  <span>Sign Out</span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Navigation Section -->
    <nav class="sidebar-section nav-section">
      <div class="sidebar-section-header" v-if="!isCollapsed">
        <h3 class="section-title">Navigation</h3>
      </div>
      <ul class="sidebar-menu">
        <li class="menu-item">
          <router-link 
            to="/dashboard"
            class="menu-link" 
            :class="{ 'is-active': activeSection === 'dashboard' }"
            :title="isCollapsed ? 'Dashboard' : ''"
            @click="handleDashboardClick"
          >
            <span class="icon">
              <i class="fas fa-tachometer-alt"></i>
            </span>
            <span v-if="!isCollapsed" class="menu-text">Dashboard</span>
          </router-link>
        </li>
        <li class="menu-item">
          <router-link 
            to="/dashboard/notes"
            class="menu-link" 
            :class="{ 'is-active': activeSection === 'all-notes' }"
            :title="isCollapsed ? 'All Notes' : ''"
          >
            <span class="icon">
              <i class="fas fa-sticky-note"></i>
            </span>
            <span v-if="!isCollapsed" class="menu-text">All Notes</span>
            <span v-if="!isCollapsed" class="menu-count">{{ noteCount }}</span>
          </router-link>
        </li>
        <li class="menu-item">
          <router-link 
            to="/dashboard/recent"
            class="menu-link" 
            :class="{ 'is-active': activeSection === 'recent' }"
            :title="isCollapsed ? 'Recent' : ''"
          >
            <span class="icon">
              <i class="fas fa-clock"></i>
            </span>
            <span v-if="!isCollapsed" class="menu-text">Recent</span>
          </router-link>
        </li>
        <li class="menu-item">
          <router-link 
            to="/dashboard/favorites"
            class="menu-link" 
            :class="{ 'is-active': activeSection === 'favorites' }"
            :title="isCollapsed ? 'Favorites' : ''"
          >
            <span class="icon">
              <i class="fas fa-star"></i>
            </span>
            <span v-if="!isCollapsed" class="menu-text">Favorites</span>
          </router-link>
        </li>
        <li class="menu-item">
          <router-link 
            to="/dashboard/shared"
            class="menu-link" 
            :class="{ 'is-active': activeSection === 'shared' }"
            :title="isCollapsed ? 'Shared' : ''"
          >
            <span class="icon">
              <i class="fas fa-share-alt"></i>
            </span>
            <span v-if="!isCollapsed" class="menu-text">Shared</span>
          </router-link>
        </li>
        <li class="menu-item">
          <router-link 
            to="/dashboard/groups"
            class="menu-link" 
            :class="{ 'is-active': activeSection === 'groups' }"
            :title="isCollapsed ? 'Groups' : ''"
          >
            <span class="icon">
              <i class="fas fa-users"></i>
            </span>
            <span v-if="!isCollapsed" class="menu-text">Groups</span>
          </router-link>
        </li>
        <li class="menu-item">
          <router-link 
            to="/dashboard/archived"
            class="menu-link" 
            :class="{ 'is-active': activeSection === 'archived' }"
            :title="isCollapsed ? 'Archived' : ''"
          >
            <span class="icon">
              <i class="fas fa-archive"></i>
            </span>
            <span v-if="!isCollapsed" class="menu-text">Archived</span>
          </router-link>
        </li>
      </ul>
    </nav>

    <!-- Admin Section -->
    <div v-if="authStore.isAdmin" class="sidebar-section admin-section">
      <div class="sidebar-section-header" v-if="!isCollapsed">
        <h3 class="section-title">Administration</h3>
      </div>
      <ul class="sidebar-menu">
        <li class="menu-item">
          <router-link 
            to="/admin"
            class="menu-link" 
            :class="{ 'is-active': activeSection === 'admin-dashboard' }"
            :title="isCollapsed ? 'Admin Dashboard' : ''"
          >
            <span class="icon">
              <i class="fas fa-shield-alt"></i>
            </span>
            <span v-if="!isCollapsed" class="menu-text">Dashboard</span>
          </router-link>
        </li>
        <li class="menu-item">
          <router-link 
            to="/admin/users"
            class="menu-link" 
            :class="{ 'is-active': activeSection === 'admin-users' }"
            :title="isCollapsed ? 'User Management' : ''"
          >
            <span class="icon">
              <i class="fas fa-users-cog"></i>
            </span>
            <span v-if="!isCollapsed" class="menu-text">Users</span>
          </router-link>
        </li>
      </ul>
    </div>

    <!-- Groups Section -->
    <div class="sidebar-section groups-section" v-if="groups.length > 0">
      <div class="sidebar-section-header" v-if="!isCollapsed">
        <h3 class="section-title">Groups</h3>
        <button class="button is-ghost is-small" @click="createGroup">
          <span class="icon">
            <i class="fas fa-plus"></i>
          </span>
        </button>
      </div>
      <ul class="sidebar-menu">
        <li v-for="group in groups" :key="group.id" class="menu-item">
          <router-link 
            :to="`/groups/${group.id}`"
            class="menu-link" 
            :class="{ 'is-active': activeSection === 'groups' && route.params.id === group.id }"
            :title="isCollapsed ? group.name : ''"
          >
            <span class="icon">
              <i class="fas fa-users"></i>
            </span>
            <span v-if="!isCollapsed" class="menu-text">{{ group.name }}</span>
            <span v-if="!isCollapsed" class="menu-count">{{ group.memberCount }}</span>
          </router-link>
        </li>
      </ul>
    </div>

    <!-- Tags Section -->
    <div class="sidebar-section tags-section">
      <div class="sidebar-section-header" v-if="!isCollapsed">
        <h3 class="section-title">Tags</h3>
        <button class="button is-ghost is-small" @click="manageTags">
          <span class="icon">
            <i class="fas fa-cog"></i>
          </span>
        </button>
      </div>
      <div v-if="!isCollapsed" class="tags-container">
        <div v-if="popularTags.length === 0" class="empty-state">
          <p class="has-text-grey">No tags yet</p>
        </div>
        <div v-else class="tags">
          <span 
            v-for="tag in popularTags" 
            :key="tag.name"
            class="tag is-clickable"
            :class="{ 'is-primary': selectedTags.includes(tag.name) }"
            @click="toggleTag(tag.name)"
          >
            {{ tag.name }}
            <span class="tag-count">{{ tag.count }}</span>
          </span>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="sidebar-section actions-section">
      <div class="sidebar-section-header" v-if="!isCollapsed">
        <h3 class="section-title">Quick Actions</h3>
      </div>
      <div class="quick-actions">
        <button 
          class="button is-primary is-fullwidth" 
          :class="{ 'is-small': isCollapsed }"
          @click="createNote"
          :title="isCollapsed ? 'Create Note' : ''"
        >
          <span class="icon">
            <i class="fas fa-plus"></i>
          </span>
          <span v-if="!isCollapsed">New Note</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '../../stores/auth'
import { useGroupsStore } from '../../stores/groups'

// Props
interface Props {
  isCollapsed: boolean
}

defineProps<Props>()

// Emits
const emit = defineEmits<{
  'toggle-collapse': []
  'close-mobile': []
  'create-note': []
  'open-settings': []
}>()

// Composables with error handling
let router: any = null
let route: any = null
let authStore: any = null

// Initialize composables safely
const initializeComposables = () => {
  try {
    if (!router) router = useRouter()
    if (!route) route = useRoute()
    if (!authStore) authStore = useAuthStore()
    return true
  } catch (error) {
    console.warn('Composables initialization error in Sidebar:', error)
    return false
  }
}

// Try initial initialization
if (!initializeComposables()) {
  // Create fallback objects
  router = { push: (path: string) => window.location.href = path }
  route = { name: '', path: window.location.pathname, params: {} }
  authStore = { 
    user: ref(null), 
    isAdmin: computed(() => {
      // Try to get real auth store value
      try {
        const realAuthStore = useAuthStore()
        return realAuthStore.isAdmin
      } catch {
        return false
      }
    }), 
    logout: () => Promise.resolve(),
    isAuthenticated: computed(() => false)
  }
}

// Initialize groups store with error handling
let groupsStore: any = null
try {
  groupsStore = useGroupsStore()
} catch (error) {
  console.warn('Groups store initialization failed:', error)
  groupsStore = { 
    groups: ref([]),
    loadGroups: () => Promise.resolve()
  }
}

// Reactive state
const selectedTags = ref<string[]>([])

// Computed active section based on current route
const activeSection = computed(() => {
  try {
    const routeName = route?.name as string || ''
    const routePath = route?.path || window.location.pathname
    
    if (routePath.includes('/admin/users')) return 'admin-users'
    if (routePath.includes('/admin')) return 'admin-dashboard'
    if (routePath.includes('/shared')) return 'shared'
    if (routePath.includes('/groups')) return 'groups'
    if (routePath.includes('/settings')) return 'settings'
    if (routePath.includes('/search')) return 'search'
    if (routePath.includes('/recent')) return 'recent'
    if (routePath.includes('/favorites')) return 'favorites'
    if (routePath.includes('/archived')) return 'archived'
    if (routePath.includes('/notes')) return 'all-notes'
    if (routeName === 'Dashboard' || routePath === '/dashboard') return 'dashboard'
    
    return 'dashboard'
  } catch (error) {
    console.warn('Error computing active section:', error)
    return 'dashboard'
  }
})

// Computed data from stores
const noteCount = ref(42) // This would come from notes store
const groups = computed(() => {
  try {
    return groupsStore?.groups?.value || groupsStore?.groups || []
  } catch (error) {
    console.warn('Error accessing groups:', error)
    return []
  }
})
const popularTags = ref([
  { name: 'work', count: 15 },
  { name: 'ideas', count: 8 },
  { name: 'todo', count: 12 },
  { name: 'meeting', count: 6 }
]) // This would come from tags store

// Methods

const toggleTag = (tagName: string) => {
  const index = selectedTags.value.indexOf(tagName)
  if (index > -1) {
    selectedTags.value.splice(index, 1)
  } else {
    selectedTags.value.push(tagName)
  }
  // Apply tag filter
}

const createNote = () => {
  // Emit create note event to parent
  emit('create-note')
}

const createGroup = () => {
  // Navigate to groups page where user can create a new group
  router.push('/dashboard/groups')
}

const manageTags = () => {
  // Open tag management modal
  console.log('Manage tags')
}

const openSettings = () => {
  // Emit settings event to parent
  emit('open-settings')
}

const handleLogout = async () => {
  await authStore.logout()
  router.push('/login')
}

const handleDashboardClick = () => {
  // Force navigation to dashboard and ensure it's the main dashboard
  try {
    // Ensure auth store is properly initialized before navigation
    if (!authStore.isInitialized && authStore.token) {
      console.log('Initializing auth before dashboard navigation...')
      authStore.initializeAuth()
    }
    
    if (router && router.push) {
      router.push('/dashboard')
    } else {
      window.location.href = '/dashboard'
    }
  } catch (error) {
    console.error('Navigation error:', error)
    // Fallback: reload the page to dashboard
    window.location.href = '/dashboard'
  }
}

// Load groups on mount and retry composable initialization
onMounted(async () => {
  // Retry composable initialization if needed
  if (!authStore || !authStore.isAdmin) {
    console.log('Retrying composable initialization in Sidebar...')
    initializeComposables()
  }
  
  try {
    if (groupsStore && typeof groupsStore.loadGroups === 'function') {
      await groupsStore.loadGroups()
    }
  } catch (error) {
    console.error('Failed to load groups in sidebar:', error)
  }
})
</script>

<style scoped>
.sidebar {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f8f9fa;
  border-right: 1px solid #e9ecef;
  overflow: hidden;
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid #e9ecef;
  min-height: 60px;
}

.sidebar-brand .title {
  margin: 0;
  color: #363636;
}

.sidebar-toggle,
.sidebar-close {
  border: none;
  background: transparent;
}

.sidebar-section {
  padding: 1rem;
  border-bottom: 1px solid #e9ecef;
}

.sidebar-section:last-child {
  border-bottom: none;
}

.sidebar-section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.75rem;
}

.section-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin: 0;
}

/* User Profile */
.user-section {
  background: white;
  border-radius: 8px;
  margin: 1rem;
  padding: 1rem;
  border-bottom: none;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.user-profile.is-collapsed {
  justify-content: center;
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-weight: 600;
  color: #363636;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-email {
  font-size: 0.875rem;
  color: #6c757d;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Navigation Menu */
.sidebar-menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

.menu-item {
  margin-bottom: 0.25rem;
}

.menu-link {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 6px;
  color: #6c757d;
  text-decoration: none;
  transition: all 0.2s ease;
  cursor: pointer;
}

.menu-link:hover {
  background: #e9ecef;
  color: #495057;
}

.menu-link.is-active {
  background: #007bff;
  color: white;
}

.menu-text {
  flex: 1;
  font-weight: 500;
}

.menu-count {
  font-size: 0.75rem;
  background: rgba(0, 0, 0, 0.1);
  color: inherit;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  min-width: 1.5rem;
  text-align: center;
}

.menu-link.is-active .menu-count {
  background: rgba(255, 255, 255, 0.2);
}

/* Tags */
.tags-container {
  max-height: 200px;
  overflow-y: auto;
}

.empty-state {
  text-align: center;
  padding: 1rem 0;
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag.is-clickable {
  cursor: pointer;
  transition: all 0.2s ease;
}

.tag.is-clickable:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tag-count {
  margin-left: 0.25rem;
  font-size: 0.75rem;
  opacity: 0.7;
}

/* Quick Actions */
.actions-section {
  margin-top: auto;
  border-top: 1px solid #e9ecef;
  border-bottom: none;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* Collapsed State */
.sidebar.is-collapsed {
  width: 60px;
}

.sidebar.is-collapsed .sidebar-section {
  padding: 0.5rem;
}

.sidebar.is-collapsed .menu-link {
  justify-content: center;
  padding: 0.75rem 0.5rem;
}

.sidebar.is-collapsed .user-profile {
  flex-direction: column;
  gap: 0.5rem;
}

.sidebar.is-collapsed .quick-actions .button {
  padding: 0.75rem 0.5rem;
}

/* Admin Dropdown Item */
.dropdown-item[href="/admin"] {
  color: #dc3545 !important;
  font-weight: 600;
}

.dropdown-item[href="/admin"]:hover {
  background-color: #dc3545 !important;
  color: white !important;
}

.dropdown-item[href="/admin"] .icon {
  color: #dc3545;
}

.dropdown-item[href="/admin"]:hover .icon {
  color: white;
}

/* Responsive */
@media screen and (max-width: 768px) {
  .sidebar {
    width: 280px;
  }
  
  .sidebar-header {
    padding: 0.75rem 1rem;
  }
}
</style>