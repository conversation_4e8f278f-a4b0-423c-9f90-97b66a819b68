<template>
  <div class="board-view">
    <div class="container is-fluid">
      <!-- Board Header -->
      <div class="board-header mb-4">
        <div class="level">
          <div class="level-left">
            <div class="level-item">
              <h1 class="title is-3">
                <span class="icon">
                  <i class="fas fa-columns"></i>
                </span>
                <span>{{ boardTitle }}</span>
              </h1>
            </div>
          </div>
          <div class="level-right">
            <div class="level-item">
              <div class="buttons">
                <button
                  class="button is-light"
                  @click="showBoardSettings = true"
                >
                  <span class="icon">
                    <i class="fas fa-cog"></i>
                  </span>
                  <span>Settings</span>
                </button>
                <button
                  class="button is-primary"
                  @click="showSharing = true"
                >
                  <span class="icon">
                    <i class="fas fa-share-alt"></i>
                  </span>
                  <span>Share</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Loading State -->
      <div v-if="isLoading" class="loading-state">
        <div class="has-text-centered">
          <span class="icon is-large">
            <i class="fas fa-spinner fa-pulse"></i>
          </span>
          <p>Loading board...</p>
        </div>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="error-state">
        <div class="has-text-centered">
          <span class="icon is-large has-text-danger">
            <i class="fas fa-exclamation-triangle"></i>
          </span>
          <h3 class="title is-4 has-text-danger">Error Loading Board</h3>
          <p class="subtitle">{{ error }}</p>
          <button class="button is-primary" @click="loadBoard">
            <span class="icon">
              <i class="fas fa-redo"></i>
            </span>
            <span>Retry</span>
          </button>
        </div>
      </div>

      <!-- Board Content -->
      <div v-else-if="board" class="board-content">
        <KanbanEditor
          :model-value="board.content"
          @update:model-value="updateBoardContent"
          :board-id="boardId"
          :read-only="!canEdit"
        />
      </div>

      <!-- Not Found State -->
      <div v-else class="not-found-state">
        <div class="has-text-centered">
          <span class="icon is-large has-text-grey-light">
            <i class="fas fa-search"></i>
          </span>
          <h3 class="title is-4 has-text-grey">Board Not Found</h3>
          <p class="subtitle">The board you're looking for doesn't exist or you don't have access to it.</p>
          <router-link to="/dashboard" class="button is-primary">
            <span class="icon">
              <i class="fas fa-home"></i>
            </span>
            <span>Go to Dashboard</span>
          </router-link>
        </div>
      </div>
    </div>

    <!-- Board Sharing Modal -->
    <BoardSharing
      v-if="showSharing && board"
      :share-settings="board.shareSettings || defaultShareSettings"
      :members="board.members || []"
      :current-user-id="currentUserId"
      :board-id="boardId"
      @close="showSharing = false"
      @update:share-settings="updateShareSettings"
      @invite-member="inviteMember"
      @remove-member="removeMember"
      @update-member-permission="updateMemberPermission"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import KanbanEditor from '../components/editors/KanbanEditor.vue'
import BoardSharing from '../components/kanban/BoardSharing.vue'
import type { BoardShareSettings } from '../types/kanban'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// Reactive state
const board = ref<any>(null)
const isLoading = ref(true)
const error = ref<string | null>(null)
const showSharing = ref(false)
const showBoardSettings = ref(false)

// Computed
const boardId = computed(() => route.params.id as string)
const boardTitle = computed(() => board.value?.title || 'Untitled Board')
const currentUserId = computed(() => authStore.user?.id || '')
const canEdit = computed(() => {
  if (!board.value || !authStore.user) return false
  
  // Owner can always edit
  if (board.value.userId === authStore.user.id) return true
  
  // Check member permissions
  const userPermission = board.value.shareSettings?.permissions?.[authStore.user.id]
  return userPermission === 'edit' || userPermission === 'admin'
})

const defaultShareSettings: BoardShareSettings = {
  isPublic: false,
  allowedUsers: [],
  permissions: {}
}

// Methods
const loadBoard = async () => {
  try {
    isLoading.value = true
    error.value = null
    
    // TODO: Replace with actual API call
    // For now, create a mock board
    await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate API delay
    
    board.value = {
      id: boardId.value,
      title: `Board ${boardId.value}`,
      content: JSON.stringify({
        columns: [
          {
            id: 'todo',
            title: 'To Do',
            cards: []
          },
          {
            id: 'in-progress',
            title: 'In Progress',
            cards: []
          },
          {
            id: 'done',
            title: 'Done',
            cards: []
          }
        ]
      }),
      userId: authStore.user?.id,
      shareSettings: defaultShareSettings,
      members: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to load board'
  } finally {
    isLoading.value = false
  }
}

const updateBoardContent = (content: string) => {
  if (board.value && canEdit.value) {
    board.value.content = content
    board.value.updatedAt = new Date().toISOString()
    // TODO: Save to API
  }
}

const updateShareSettings = (settings: BoardShareSettings) => {
  if (board.value) {
    board.value.shareSettings = settings
    // TODO: Save to API
  }
}

const inviteMember = (email: string, permission: string) => {
  // TODO: Implement member invitation
  console.log('Invite member:', email, permission)
}

const removeMember = (memberId: string) => {
  if (board.value) {
    board.value.members = board.value.members.filter((m: any) => m.id !== memberId)
    // TODO: Save to API
  }
}

const updateMemberPermission = (memberId: string, permission: string) => {
  if (board.value && board.value.shareSettings) {
    board.value.shareSettings.permissions[memberId] = permission
    // TODO: Save to API
  }
}

// Lifecycle
onMounted(() => {
  loadBoard()
})
</script>

<style scoped>
.board-view {
  min-height: 100vh;
  padding: 1rem 0;
}

.board-header {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.board-content {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  min-height: 600px;
}

.loading-state,
.error-state,
.not-found-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Responsive design */
@media (max-width: 768px) {
  .board-view {
    padding: 0.5rem 0;
  }
  
  .board-header {
    margin: 0 0.5rem 1rem 0.5rem;
    padding: 1rem;
  }
  
  .board-content {
    margin: 0 0.5rem;
    padding: 0.5rem;
  }
  
  .level {
    display: block;
  }
  
  .level-left,
  .level-right {
    margin-bottom: 1rem;
  }
  
  .title.is-3 {
    font-size: 1.5rem;
  }
}
</style>