import { defineStore } from 'pinia'
import { ref, computed, nextTick } from 'vue'
import { userService, type UserPreferences } from '../services/userService'
import { useAuthStore } from './auth'

export const useSettingsStore = defineStore('settings', () => {
  const preferences = ref<UserPreferences>({
    theme: 'auto',
    language: 'en',
    timezone: 'UTC',
    autoSaveInterval: 30000,
    notifications: {
      email: true,
      push: true,
      mentions: true
    }
  })
  
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const isInitialized = ref(false)

  const isDarkMode = computed(() => {
    if (preferences.value.theme === 'dark') return true
    if (preferences.value.theme === 'light') return false
    // Auto mode - check system preference
    return window.matchMedia('(prefers-color-scheme: dark)').matches
  })

  const loadSettings = async () => {
    isLoading.value = true
    error.value = null

    try {
      const response = await userService.getSettings()
      
      if (response.success && response.data) {
        preferences.value = response.data.preferences
        // Defer theme application to next tick to avoid blocking initialization
        nextTick(() => {
          applyTheme()
        })
      } else {
        // If failed to load settings, try to load theme from authStore
        const authStore = useAuthStore()
        const savedTheme = authStore.loadThemePreference()
        if (savedTheme) {
          preferences.value.theme = savedTheme
          nextTick(() => {
            applyTheme()
          })
        }
        error.value = response.error || 'Failed to load settings'
      }
    } catch (err) {
      // If failed to load settings, try to load theme from authStore
      const authStore = useAuthStore()
      const savedTheme = authStore.loadThemePreference()
      if (savedTheme) {
        preferences.value.theme = savedTheme
        nextTick(() => {
          applyTheme()
        })
      }
      error.value = err instanceof Error ? err.message : 'Failed to load settings'
    } finally {
      isLoading.value = false
    }
  }

  const updateSettings = async (newPreferences: Partial<UserPreferences>) => {
    isLoading.value = true
    error.value = null

    try {
      const response = await userService.updateSettings(newPreferences)
      
      if (response.success && response.data) {
        preferences.value = response.data.preferences
        // Save theme preference to authStore for persistence across sessions
        if (newPreferences.theme) {
          const authStore = useAuthStore()
          authStore.saveThemePreference(newPreferences.theme)
        }
        // Defer theme application to next tick to avoid blocking UI updates
        nextTick(() => {
          applyTheme()
        })
        return { success: true, message: response.message }
      } else {
        error.value = response.error || 'Failed to update settings'
        return { success: false, error: error.value }
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to update settings'
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }

  const updateTheme = async (theme: 'light' | 'dark' | 'auto') => {
    return await updateSettings({ theme })
  }

  const updateLanguage = async (language: string) => {
    return await updateSettings({ language })
  }

  const updateAutoSaveInterval = async (interval: number) => {
    return await updateSettings({ autoSaveInterval: interval })
  }

  const updateNotifications = async (notifications: Partial<UserPreferences['notifications']>) => {
    const updatedNotifications = { ...preferences.value.notifications, ...notifications }
    return await updateSettings({ notifications: updatedNotifications })
  }

  const applyTheme = () => {
    // Defer DOM operations to avoid blocking during initialization
    const applyThemeToDOM = () => {
      const html = document.documentElement
      
      if (preferences.value.theme === 'dark') {
        html.classList.add('dark')
        html.classList.remove('light')
      } else if (preferences.value.theme === 'light') {
        html.classList.add('light')
        html.classList.remove('dark')
      } else {
        // Auto mode
        html.classList.remove('dark', 'light')
        if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
          html.classList.add('dark')
        } else {
          html.classList.add('light')
        }
      }
    }
    
    // Use nextTick to defer DOM manipulation
    nextTick(applyThemeToDOM)
  }

  const initializeSettings = async () => {
    if (isInitialized.value) {
      console.log('Settings already initialized, skipping...')
      return
    }
    
    await loadSettings()
    
    // Listen for system theme changes when in auto mode with deferred execution
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    mediaQuery.addEventListener('change', () => {
      if (preferences.value.theme === 'auto') {
        // Defer theme change to avoid blocking the main thread
        nextTick(() => {
          applyTheme()
        })
      }
    })
    
    isInitialized.value = true
  }

  const resetInitialization = () => {
    isInitialized.value = false
  }

  return {
    preferences,
    isLoading,
    error,
    isDarkMode,
    isInitialized,
    loadSettings,
    updateSettings,
    updateTheme,
    updateLanguage,
    updateAutoSaveInterval,
    updateNotifications,
    applyTheme,
    initializeSettings,
    resetInitialization
  }
})