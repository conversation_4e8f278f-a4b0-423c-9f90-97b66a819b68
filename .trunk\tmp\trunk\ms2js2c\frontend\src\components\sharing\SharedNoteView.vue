<template>
  <div class="shared-note-view">
    <!-- Password Protection Modal -->
    <div v-if="showPasswordModal" class="modal is-active">
      <div class="modal-background"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">
            <span class="icon">
              <i class="fas fa-lock"></i>
            </span>
            Password Required
          </p>
        </header>
        <section class="modal-card-body">
          <div v-if="passwordError" class="notification is-danger">
            {{ passwordError }}
          </div>
          <div class="field">
            <label class="label">Enter password to access this shared note</label>
            <div class="control">
              <input 
                type="password" 
                class="input" 
                v-model="password"
                placeholder="Password"
                @keyup.enter="submitPassword"
                ref="passwordInput"
              >
            </div>
          </div>
        </section>
        <footer class="modal-card-foot">
          <button 
            class="button is-primary" 
            @click="submitPassword"
            :class="{ 'is-loading': isLoading }"
            :disabled="!password || isLoading"
          >
            Access Note
          </button>
        </footer>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading && !showPasswordModal" class="section">
      <div class="container has-text-centered">
        <div class="is-loading-spinner"></div>
        <p class="mt-4">Loading shared note...</p>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error && !showPasswordModal" class="section">
      <div class="container">
        <div class="notification is-danger">
          <h1 class="title">Unable to Access Shared Note</h1>
          <p>{{ error }}</p>
          <div class="mt-4">
            <router-link to="/" class="button is-primary">
              Go to Home
            </router-link>
          </div>
        </div>
      </div>
    </div>

    <!-- Shared Note Content -->
    <div v-else-if="sharedNote" class="section">
      <div class="container">
        <!-- Watermark Notice -->
        <div v-if="sharedNote.watermark" class="notification is-info is-light">
          <div class="level">
            <div class="level-left">
              <div class="level-item">
                <span class="icon">
                  <i class="fas fa-stamp"></i>
                </span>
                <span>{{ sharedNote.watermark.text }}</span>
              </div>
            </div>
            <div class="level-right">
              <div class="level-item">
                <small>{{ formatDate(sharedNote.watermark.timestamp) }}</small>
              </div>
            </div>
          </div>
        </div>

        <!-- Share Info -->
        <div class="box mb-5">
          <div class="level">
            <div class="level-left">
              <div class="level-item">
                <div>
                  <h1 class="title is-4">{{ sharedNote.note.title }}</h1>
                  <p class="subtitle is-6">
                    <span class="tag" :class="getNoteTypeColor(sharedNote.note.noteType)">
                      {{ getNoteTypeDisplayName(sharedNote.note.noteType) }}
                    </span>
                    <span class="tag" :class="getAccessLevelColor(sharedNote.share.accessLevel)">
                      {{ getAccessLevelDisplayName(sharedNote.share.accessLevel) }}
                    </span>
                  </p>
                </div>
              </div>
            </div>
            <div class="level-right">
              <div class="level-item">
                <div class="field is-grouped">
                  <div class="control">
                    <div class="tags">
                      <span 
                        v-for="permission in sharedNote.share.permissions" 
                        :key="permission"
                        class="tag" 
                        :class="getPermissionColor(permission)"
                      >
                        {{ getPermissionDisplayName(permission) }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Expiration Warning -->
          <div v-if="sharedNote.share.expiresAt" class="notification is-warning is-light">
            <p>
              <span class="icon">
                <i class="fas fa-clock"></i>
              </span>
              This shared note {{ formatExpirationDate(sharedNote.share.expiresAt) }}
            </p>
          </div>
        </div>

        <!-- Note Content -->
        <div class="note-content">
          <!-- Rich Text Content -->
          <div 
            v-if="sharedNote.note.noteType === 'richtext'" 
            class="content"
            v-html="sharedNote.note.content"
          ></div>

          <!-- Markdown Content -->
          <div 
            v-else-if="sharedNote.note.noteType === 'markdown'" 
            class="content markdown-content"
          >
            <MarkdownRenderer :content="sharedNote.note.content" />
          </div>

          <!-- Kanban Content -->
          <div 
            v-else-if="sharedNote.note.noteType === 'kanban'" 
            class="kanban-content"
          >
            <KanbanRenderer :content="sharedNote.note.content" :readonly="true" />
          </div>
        </div>

        <!-- Note Metadata -->
        <div class="box mt-5">
          <h6 class="title is-6">Note Information</h6>
          <div class="columns">
            <div class="column">
              <p><strong>Created:</strong> {{ formatDate(sharedNote.note.createdAt) }}</p>
              <p><strong>Last Updated:</strong> {{ formatDate(sharedNote.note.updatedAt) }}</p>
            </div>
            <div class="column">
              <p v-if="sharedNote.note.metadata.wordCount">
                <strong>Word Count:</strong> {{ sharedNote.note.metadata.wordCount }}
              </p>
              <p v-if="sharedNote.note.metadata.readingTime">
                <strong>Reading Time:</strong> {{ sharedNote.note.metadata.readingTime }} min
              </p>
            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="has-text-centered mt-5">
          <div class="field is-grouped is-grouped-centered">
            <div class="control">
              <button class="button is-info" @click="printNote">
                <span class="icon">
                  <i class="fas fa-print"></i>
                </span>
                <span>Print</span>
              </button>
            </div>
            <div v-if="canEdit" class="control">
              <button class="button is-primary" @click="editNote">
                <span class="icon">
                  <i class="fas fa-edit"></i>
                </span>
                <span>Edit</span>
              </button>
            </div>
            <div v-if="canComment" class="control">
              <button class="button is-warning" @click="addComment">
                <span class="icon">
                  <i class="fas fa-comment"></i>
                </span>
                <span>Comment</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useNoteSharesStore } from '../../stores/noteShares';
import { 
  getAccessLevelDisplayName,
  getAccessLevelColor,
  getPermissionDisplayName,
  getPermissionColor,
  formatExpirationDate,
  type SharedNoteResponse
} from '../../types/noteShare';
import MarkdownRenderer from '../editors/MarkdownRenderer.vue';
import KanbanRenderer from '../editors/KanbanRenderer.vue';

const route = useRoute();
const router = useRouter();
const shareStore = useNoteSharesStore();

// Reactive state
const sharedNote = ref<SharedNoteResponse | null>(null);
const showPasswordModal = ref(false);
const password = ref('');
const passwordError = ref('');
const passwordInput = ref<HTMLInputElement>();

// Computed properties
const isLoading = computed(() => shareStore.isLoading);
const error = computed(() => shareStore.error);

const canEdit = computed(() => {
  return sharedNote.value?.share.permissions.includes('edit') || false;
});

const canComment = computed(() => {
  return sharedNote.value?.share.permissions.includes('comment') || false;
});

// Methods
const loadSharedNote = async (shareToken: string, password?: string) => {
  try {
    const note = await shareStore.accessSharedNote(shareToken, password);
    sharedNote.value = note;
    showPasswordModal.value = false;
    passwordError.value = '';
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Failed to access shared note';
    
    if (errorMessage.includes('Password required') || errorMessage.includes('Invalid password')) {
      showPasswordModal.value = true;
      passwordError.value = errorMessage.includes('Invalid password') ? 'Invalid password. Please try again.' : '';
      
      // Focus password input
      nextTick(() => {
        passwordInput.value?.focus();
      });
    } else {
      shareStore.setError(errorMessage);
    }
  }
};

const submitPassword = async () => {
  if (!password.value) return;
  
  passwordError.value = '';
  const shareToken = route.params.token as string;
  await loadSharedNote(shareToken, password.value);
};

const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { 
    hour: '2-digit', 
    minute: '2-digit' 
  });
};

const getNoteTypeDisplayName = (noteType: string): string => {
  switch (noteType) {
    case 'richtext':
      return 'Rich Text';
    case 'markdown':
      return 'Markdown';
    case 'kanban':
      return 'Kanban Board';
    default:
      return 'Unknown';
  }
};

const getNoteTypeColor = (noteType: string): string => {
  switch (noteType) {
    case 'richtext':
      return 'is-info';
    case 'markdown':
      return 'is-success';
    case 'kanban':
      return 'is-warning';
    default:
      return 'is-light';
  }
};

const printNote = () => {
  window.print();
};

const editNote = () => {
  // TODO: Implement collaborative editing
  console.log('Edit note functionality not yet implemented');
};

const addComment = () => {
  // TODO: Implement commenting system
  console.log('Comment functionality not yet implemented');
};

// Lifecycle
onMounted(() => {
  const shareToken = route.params.token as string;
  if (shareToken) {
    loadSharedNote(shareToken);
  } else {
    router.push('/');
  }
});
</script>

<style scoped>
.shared-note-view {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.note-content {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.markdown-content {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
}

.kanban-content {
  min-height: 400px;
}

.is-loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media print {
  .box:first-child,
  .has-text-centered,
  .notification.is-info.is-light {
    display: none !important;
  }
  
  .note-content {
    box-shadow: none;
    padding: 0;
  }
}
</style>