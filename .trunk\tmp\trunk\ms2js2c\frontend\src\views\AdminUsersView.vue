<template>
  <div class="admin-users">
    <div class="container is-fluid">
      <!-- Header -->
      <div class="level mb-6">
        <div class="level-left">
          <div class="level-item">
            <div>
              <h1 class="title is-3">User Management</h1>
              <p class="subtitle is-6">Manage user accounts and permissions</p>
            </div>
          </div>
        </div>
        <div class="level-right">
          <div class="level-item">
            <div class="field is-grouped">
              <div class="control">
                <button 
                  class="button is-primary" 
                  @click="loadUsers"
                  :class="{ 'is-loading': isLoading }"
                >
                  <span class="icon">
                    <i class="fas fa-sync-alt"></i>
                  </span>
                  <span>Refresh</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Filters -->
      <div class="card mb-5">
        <div class="card-content">
          <div class="columns">
            <div class="column is-4">
              <div class="field">
                <label class="label">Search</label>
                <div class="control has-icons-left">
                  <input 
                    v-model="searchQuery" 
                    class="input" 
                    type="text" 
                    placeholder="Search by email or name..."
                    @input="debouncedSearch"
                  >
                  <span class="icon is-small is-left">
                    <i class="fas fa-search"></i>
                  </span>
                </div>
              </div>
            </div>
            <div class="column is-2">
              <div class="field">
                <label class="label">Status</label>
                <div class="control">
                  <div class="select is-fullwidth">
                    <select v-model="statusFilter" @change="loadUsers">
                      <option value="">All Status</option>
                      <option value="active">Active</option>
                      <option value="suspended">Suspended</option>
                      <option value="banned">Banned</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
            <div class="column is-2">
              <div class="field">
                <label class="label">Sort By</label>
                <div class="control">
                  <div class="select is-fullwidth">
                    <select v-model="sortBy" @change="loadUsers">
                      <option value="created_at">Created Date</option>
                      <option value="email">Email</option>
                      <option value="display_name">Name</option>
                      <option value="updated_at">Last Updated</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
            <div class="column is-2">
              <div class="field">
                <label class="label">Order</label>
                <div class="control">
                  <div class="select is-fullwidth">
                    <select v-model="sortOrder" @change="loadUsers">
                      <option value="desc">Descending</option>
                      <option value="asc">Ascending</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Error Message -->
      <div v-if="error" class="notification is-danger mb-5">
        <button class="delete" @click="error = null"></button>
        {{ error }}
      </div>

      <!-- Users Table -->
      <div class="card">
        <div class="card-content">
          <div v-if="isLoading && users.length === 0" class="has-text-centered py-6">
            <div class="is-size-4 mb-3">
              <i class="fas fa-spinner fa-spin"></i>
            </div>
            <p>Loading users...</p>
          </div>

          <div v-else-if="users.length === 0" class="has-text-centered py-6">
            <div class="is-size-4 mb-3">
              <i class="fas fa-users"></i>
            </div>
            <p class="has-text-grey">No users found</p>
          </div>

          <div v-else class="table-container">
            <table class="table is-fullwidth is-hoverable">
              <thead>
                <tr>
                  <th>User</th>
                  <th>Status</th>
                  <th>Stats</th>
                  <th>Created</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="user in users" :key="user.id">
                  <td>
                    <div class="media">
                      <div class="media-left">
                        <figure class="image is-32x32">
                          <img 
                            v-if="user.avatar_url" 
                            :src="user.avatar_url" 
                            :alt="user.display_name"
                            class="is-rounded"
                          >
                          <div v-else class="avatar-placeholder is-rounded">
                            {{ user.display_name.charAt(0).toUpperCase() }}
                          </div>
                        </figure>
                      </div>
                      <div class="media-content">
                        <div class="content">
                          <p>
                            <strong>{{ user.display_name }}</strong>
                            <span v-if="user.isAdmin" class="tag is-warning is-small ml-2">Admin</span>
                            <br>
                            <small class="has-text-grey">{{ user.email }}</small>
                            <span v-if="user.oauth_provider" class="tag is-info is-small ml-2">
                              {{ user.oauth_provider }}
                            </span>
                          </p>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td>
                    <span class="tag" :class="{
                      'is-success': user.status === 'active',
                      'is-warning': user.status === 'suspended',
                      'is-danger': user.status === 'banned'
                    }">
                      {{ user.status }}
                    </span>
                    <br>
                    <span v-if="user.email_verified" class="tag is-success is-small mt-1">
                      <span class="icon is-small">
                        <i class="fas fa-check"></i>
                      </span>
                      <span>Verified</span>
                    </span>
                    <span v-else class="tag is-warning is-small mt-1">
                      <span class="icon is-small">
                        <i class="fas fa-exclamation-triangle"></i>
                      </span>
                      <span>Unverified</span>
                    </span>
                  </td>
                  <td>
                    <div class="content">
                      <p class="is-size-7">
                        <strong>{{ user.note_count }}</strong> notes<br>
                        <strong>{{ user.group_count }}</strong> groups
                      </p>
                    </div>
                  </td>
                  <td>
                    <div class="content">
                      <p class="is-size-7">
                        {{ formatDate(user.created_at) }}
                        <br>
                        <span class="has-text-grey">{{ formatRelativeDate(user.created_at) }}</span>
                      </p>
                    </div>
                  </td>
                  <td>
                    <div class="dropdown" :class="{ 'is-active': activeDropdown === user.id }">
                      <div class="dropdown-trigger">
                        <button 
                          class="button is-small" 
                          @click="toggleDropdown(user.id)"
                          aria-haspopup="true" 
                          aria-controls="dropdown-menu"
                        >
                          <span class="icon is-small">
                            <i class="fas fa-ellipsis-v"></i>
                          </span>
                        </button>
                      </div>
                      <div class="dropdown-menu" role="menu">
                        <div class="dropdown-content">
                          <a 
                            v-if="user.status === 'active'" 
                            class="dropdown-item" 
                            @click="showStatusModal(user, 'suspended')"
                          >
                            <span class="icon">
                              <i class="fas fa-pause"></i>
                            </span>
                            <span>Suspend User</span>
                          </a>
                          <a 
                            v-if="user.status === 'suspended'" 
                            class="dropdown-item" 
                            @click="updateUserStatus(user.id, 'active')"
                          >
                            <span class="icon">
                              <i class="fas fa-play"></i>
                            </span>
                            <span>Activate User</span>
                          </a>
                          <a 
                            v-if="user.status !== 'banned'" 
                            class="dropdown-item has-text-danger" 
                            @click="showStatusModal(user, 'banned')"
                          >
                            <span class="icon">
                              <i class="fas fa-ban"></i>
                            </span>
                            <span>Ban User</span>
                          </a>
                          <hr class="dropdown-divider">
                          <a 
                            class="dropdown-item" 
                            @click="toggleUserAdmin(user)"
                          >
                            <span class="icon">
                              <i class="fas" :class="user.isAdmin ? 'fa-user-minus' : 'fa-user-plus'"></i>
                            </span>
                            <span>{{ user.isAdmin ? 'Remove Admin' : 'Make Admin' }}</span>
                          </a>
                        </div>
                      </div>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          <nav v-if="usersPagination.totalPages > 1" class="pagination is-centered mt-5" role="navigation">
            <button 
              class="pagination-previous" 
              :disabled="usersPagination.page <= 1"
              @click="changePage(usersPagination.page - 1)"
            >
              Previous
            </button>
            <button 
              class="pagination-next" 
              :disabled="usersPagination.page >= usersPagination.totalPages"
              @click="changePage(usersPagination.page + 1)"
            >
              Next
            </button>
            <ul class="pagination-list">
              <li v-for="page in visiblePages" :key="page">
                <button 
                  v-if="page !== '...'"
                  class="pagination-link" 
                  :class="{ 'is-current': page === usersPagination.page }"
                  @click="changePage(page as number)"
                >
                  {{ page }}
                </button>
                <span v-else class="pagination-ellipsis">&hellip;</span>
              </li>
            </ul>
          </nav>
        </div>
      </div>
    </div>

    <!-- Status Change Modal -->
    <div class="modal" :class="{ 'is-active': showStatusChangeModal }">
      <div class="modal-background" @click="closeStatusModal"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">
            {{ statusModalData.action === 'suspended' ? 'Suspend' : 'Ban' }} User
          </p>
          <button class="delete" @click="closeStatusModal"></button>
        </header>
        <section class="modal-card-body">
          <div class="content">
            <p>
              Are you sure you want to {{ statusModalData.action }} 
              <strong>{{ statusModalData.user?.display_name }}</strong>?
            </p>
            <div class="field">
              <label class="label">Reason (optional)</label>
              <div class="control">
                <textarea 
                  v-model="statusModalData.reason" 
                  class="textarea" 
                  placeholder="Enter reason for this action..."
                  rows="3"
                ></textarea>
              </div>
            </div>
          </div>
        </section>
        <footer class="modal-card-foot">
          <button 
            class="button is-danger" 
            @click="confirmStatusChange"
            :class="{ 'is-loading': isLoading }"
          >
            {{ statusModalData.action === 'suspended' ? 'Suspend' : 'Ban' }} User
          </button>
          <button class="button" @click="closeStatusModal">Cancel</button>
        </footer>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAdminStore } from '../stores/admin'
import { storeToRefs } from 'pinia'

const adminStore = useAdminStore()
const { users, usersPagination, isLoading, error } = storeToRefs(adminStore)

// Filters and search
const searchQuery = ref('')
const statusFilter = ref('')
const sortBy = ref('created_at')
const sortOrder = ref('desc')

// UI state
const activeDropdown = ref<string | null>(null)
const showStatusChangeModal = ref(false)
const statusModalData = ref({
  user: null as any,
  action: '',
  reason: ''
})

// Debounced search
let searchTimeout: number
const debouncedSearch = () => {
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    loadUsers()
  }, 500)
}

const loadUsers = () => {
  adminStore.loadUsers({
    page: usersPagination.value.page,
    limit: usersPagination.value.limit,
    search: searchQuery.value,
    status: statusFilter.value,
    sortBy: sortBy.value,
    sortOrder: sortOrder.value
  })
}

const changePage = (page: number) => {
  adminStore.loadUsers({
    page,
    limit: usersPagination.value.limit,
    search: searchQuery.value,
    status: statusFilter.value,
    sortBy: sortBy.value,
    sortOrder: sortOrder.value
  })
}

const visiblePages = computed(() => {
  const current = usersPagination.value.page
  const total = usersPagination.value.totalPages
  const pages: (number | string)[] = []
  
  if (total <= 7) {
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
  } else {
    pages.push(1)
    if (current > 4) pages.push('...')
    
    const start = Math.max(2, current - 2)
    const end = Math.min(total - 1, current + 2)
    
    for (let i = start; i <= end; i++) {
      pages.push(i)
    }
    
    if (current < total - 3) pages.push('...')
    pages.push(total)
  }
  
  return pages
})

const toggleDropdown = (userId: string) => {
  activeDropdown.value = activeDropdown.value === userId ? null : userId
}

const showStatusModal = (user: any, action: string) => {
  statusModalData.value = {
    user,
    action,
    reason: ''
  }
  showStatusChangeModal.value = true
  activeDropdown.value = null
}

const closeStatusModal = () => {
  showStatusChangeModal.value = false
  statusModalData.value = {
    user: null,
    action: '',
    reason: ''
  }
}

const confirmStatusChange = async () => {
  if (!statusModalData.value.user) return
  
  await updateUserStatus(
    statusModalData.value.user.id, 
    statusModalData.value.action, 
    statusModalData.value.reason
  )
  closeStatusModal()
}

const updateUserStatus = async (userId: string, status: string, reason?: string) => {
  const result = await adminStore.updateUserStatus(userId, status, reason)
  if (result.success) {
    // Optionally show success message
  }
}

const toggleUserAdmin = async (user: any) => {
  const result = await adminStore.toggleAdminStatus(user.id, !user.isAdmin)
  if (result.success) {
    // Optionally show success message
  }
  activeDropdown.value = null
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString()
}

const formatRelativeDate = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
  
  if (diffDays === 0) return 'Today'
  if (diffDays === 1) return 'Yesterday'
  if (diffDays < 7) return `${diffDays} days ago`
  if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`
  if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`
  return `${Math.floor(diffDays / 365)} years ago`
}

// Close dropdown when clicking outside
document.addEventListener('click', (e) => {
  if (!(e.target as Element).closest('.dropdown')) {
    activeDropdown.value = null
  }
})

onMounted(() => {
  loadUsers()
})
</script>

<style scoped>
.admin-users {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 2rem 0;
}

.card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.avatar-placeholder {
  width: 32px;
  height: 32px;
  background-color: #3273dc;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 0.875rem;
}

.table-container {
  overflow-x: auto;
}

.dropdown {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  z-index: 20;
  min-width: 12rem;
}

.media {
  align-items: center;
}

.media-content {
  overflow: visible;
}

.tag.is-small {
  font-size: 0.65rem;
}
</style>