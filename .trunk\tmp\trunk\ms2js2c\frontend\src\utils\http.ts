import { useAuthStore } from '../stores/auth'

interface RequestConfig {
  method?: string
  headers?: Record<string, string>
  body?: string
}

interface ApiResponse<T = any> {
  data?: T
  error?: string
  message?: string
}

class HttpClient {
  private baseURL: string
  private refreshPromise: Promise<boolean> | null = null

  constructor(baseURL = '/api') {
    this.baseURL = baseURL
  }

  private async makeRequest<T>(
    endpoint: string, 
    config: RequestConfig = {}
  ): Promise<ApiResponse<T>> {
    const authStore = useAuthStore()
    const url = `${this.baseURL}${endpoint}`
    
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...config.headers
    }

    // Add authorization header if token exists
    if (authStore.token) {
      headers.Authorization = `Bearer ${authStore.token}`
    }

    try {
      const response = await fetch(url, {
        ...config,
        headers
      })

      // Handle 401 Unauthorized - attempt token refresh
      if (response.status === 401 && authStore.token) {
        const refreshed = await this.handleTokenRefresh()
        
        if (refreshed) {
          // Retry the original request with new token
          headers.Authorization = `Bearer ${authStore.token}`
          const retryResponse = await fetch(url, {
            ...config,
            headers
          })
          
          if (retryResponse.ok) {
            // Handle 204 No Content responses
            if (retryResponse.status === 204) {
              return { data: undefined as any }
            }
            
            const data = await retryResponse.json()
            return { data }
          }
        }
        
        // If refresh failed or retry failed, clear auth and redirect
        authStore.clearTokens()
        window.location.href = '/login'
        throw new Error('Authentication failed')
      }

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || `HTTP ${response.status}`)
      }

      // Handle 204 No Content responses
      if (response.status === 204) {
        return { data: undefined as any }
      }

      const data = await response.json()
      return { data }
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Network error'
      return { error: message }
    }
  }

  private async handleTokenRefresh(): Promise<boolean> {
    const authStore = useAuthStore()
    
    // Prevent multiple simultaneous refresh attempts
    if (this.refreshPromise) {
      return this.refreshPromise
    }

    this.refreshPromise = authStore.refreshAccessToken()
    
    try {
      const result = await this.refreshPromise
      return result
    } finally {
      this.refreshPromise = null
    }
  }

  async get<T>(endpoint: string, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, { method: 'GET', headers })
  }

  async post<T>(
    endpoint: string, 
    data?: any, 
    headers?: Record<string, string>
  ): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, {
      method: 'POST',
      headers,
      body: data ? JSON.stringify(data) : undefined
    })
  }

  async put<T>(
    endpoint: string, 
    data?: any, 
    headers?: Record<string, string>
  ): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, {
      method: 'PUT',
      headers,
      body: data ? JSON.stringify(data) : undefined
    })
  }

  async delete<T>(endpoint: string, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, { method: 'DELETE', headers })
  }
}

export const httpClient = new HttpClient()
export const http = httpClient
export default httpClient