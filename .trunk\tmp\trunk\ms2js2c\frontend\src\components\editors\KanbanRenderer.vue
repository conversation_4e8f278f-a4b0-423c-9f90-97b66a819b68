<template>
  <div class="kanban-renderer">
    <div v-if="kanbanData" class="kanban-board">
      <div 
        v-for="column in kanbanData.columns" 
        :key="column.id"
        class="kanban-column"
      >
        <div class="column-header">
          <h4 class="column-title">{{ column.title }}</h4>
          <span class="card-count">{{ column.cards?.length || 0 }}</span>
        </div>
        <div class="column-cards">
          <div 
            v-for="card in column.cards || []" 
            :key="card.id"
            class="kanban-card"
          >
            <h5 class="card-title">{{ card.title }}</h5>
            <p v-if="card.description" class="card-description">{{ card.description }}</p>
            <div v-if="card.labels" class="card-labels">
              <span 
                v-for="label in card.labels" 
                :key="label"
                class="tag is-small"
                :class="getLabelColor(label)"
              >
                {{ label }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-else class="empty-kanban">
      <p>Invalid Kanban data</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  content: string;
  readonly?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false
});

const kanbanData = computed(() => {
  try {
    return JSON.parse(props.content);
  } catch (error) {
    console.error('Failed to parse Kanban content:', error);
    return null;
  }
});

const getLabelColor = (label: string): string => {
  const colors = ['is-primary', 'is-info', 'is-success', 'is-warning', 'is-danger'];
  const hash = label.split('').reduce((a, b) => {
    a = ((a << 5) - a) + b.charCodeAt(0);
    return a & a;
  }, 0);
  return colors[Math.abs(hash) % colors.length];
};
</script>

<style scoped>
.kanban-renderer {
  width: 100%;
  height: 100%;
  overflow-x: auto;
}

.kanban-board {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  min-height: 400px;
}

.kanban-column {
  flex: 0 0 300px;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid #e9ecef;
}

.column-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #dee2e6;
}

.column-title {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
  color: #495057;
}

.card-count {
  background: #6c757d;
  color: white;
  border-radius: 12px;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.column-cards {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.kanban-card {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 0.75rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s ease;
}

.kanban-card:hover {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.card-title {
  font-size: 0.875rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #212529;
}

.card-description {
  font-size: 0.75rem;
  color: #6c757d;
  margin: 0 0 0.5rem 0;
  line-height: 1.4;
}

.card-labels {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.empty-kanban {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #6c757d;
  font-style: italic;
}
</style>