<template>
  <div class="modal is-active">
    <div class="modal-background" @click="$emit('close')"></div>
    <div class="modal-card card-details-modal">
      <header class="modal-card-head">
        <div class="card-header-content">
          <div class="card-title-section">
            <i class="fas fa-credit-card card-icon"></i>
            <input
              v-if="isEditingTitle"
              v-model="localCard.title"
              @blur="saveTitle"
              @keydown.enter="saveTitle"
              @keydown.escape="cancelTitleEdit"
              class="input is-medium title-input"
              ref="titleInput"
            />
            <h2
              v-else
              @click="startTitleEdit"
              class="modal-card-title editable-title"
            >
              {{ localCard.title }}
            </h2>
          </div>
          <div class="card-meta">
            <span class="column-name">in {{ columnTitle }}</span>
          </div>
        </div>
        <button class="delete" @click="$emit('close')" aria-label="close"></button>
      </header>

      <section class="modal-card-body">
        <div class="card-details-grid">
          <!-- Main Content -->
          <div class="main-content">
            <!-- Labels -->
            <div v-if="localCard.labels.length > 0 || showLabelEditor" class="detail-section">
              <h4 class="detail-title">Labels</h4>
              <div class="labels-container">
                <span
                  v-for="label in localCard.labels"
                  :key="label.id"
                  class="tag label-tag"
                  :style="{ backgroundColor: label.color }"
                >
                  {{ label.name }}
                  <button
                    @click="removeLabel(label.id)"
                    class="delete is-small"
                  ></button>
                </span>
                <button
                  v-if="!showLabelEditor"
                  @click="showLabelEditor = true"
                  class="button is-small is-light"
                >
                  <i class="fas fa-plus"></i>
                  <span>Add Label</span>
                </button>
              </div>
              
              <!-- Label Editor -->
              <div v-if="showLabelEditor" class="label-editor">
                <div class="field has-addons">
                  <div class="control is-expanded">
                    <input
                      v-model="newLabel.name"
                      class="input is-small"
                      placeholder="Label name"
                    />
                  </div>
                  <div class="control">
                    <input
                      v-model="newLabel.color"
                      type="color"
                      class="input is-small color-input"
                    />
                  </div>
                  <div class="control">
                    <button
                      @click="addLabel"
                      class="button is-small is-primary"
                      :disabled="!newLabel.name.trim()"
                    >
                      Add
                    </button>
                  </div>
                  <div class="control">
                    <button
                      @click="cancelLabelEdit"
                      class="button is-small"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Description -->
            <div class="detail-section">
              <div class="detail-header">
                <h4 class="detail-title">
                  <i class="fas fa-align-left"></i>
                  Description
                </h4>
                <button
                  v-if="!isEditingDescription"
                  @click="startDescriptionEdit"
                  class="button is-small is-light"
                >
                  Edit
                </button>
              </div>
              
              <div v-if="isEditingDescription" class="description-editor">
                <textarea
                  v-model="localCard.description"
                  class="textarea"
                  placeholder="Add a more detailed description..."
                  rows="4"
                  ref="descriptionTextarea"
                ></textarea>
                <div class="field is-grouped">
                  <div class="control">
                    <button
                      @click="saveDescription"
                      class="button is-primary is-small"
                    >
                      Save
                    </button>
                  </div>
                  <div class="control">
                    <button
                      @click="cancelDescriptionEdit"
                      class="button is-small"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              </div>
              
              <div
                v-else-if="localCard.description"
                @click="startDescriptionEdit"
                class="description-display"
              >
                {{ localCard.description }}
              </div>
              
              <div
                v-else
                @click="startDescriptionEdit"
                class="description-placeholder"
              >
                Add a more detailed description...
              </div>
            </div>

            <!-- Checklist -->
            <div class="detail-section">
              <div class="detail-header">
                <h4 class="detail-title">
                  <i class="fas fa-check-square"></i>
                  Checklist
                  <span v-if="localCard.checklist.length > 0" class="checklist-progress">
                    {{ completedChecklistItems }}/{{ localCard.checklist.length }}
                  </span>
                </h4>
                <button
                  @click="addChecklistItem"
                  class="button is-small is-light"
                >
                  <i class="fas fa-plus"></i>
                  Add Item
                </button>
              </div>
              
              <div v-if="localCard.checklist.length > 0" class="checklist-progress-bar">
                <progress
                  class="progress is-primary"
                  :value="completedChecklistItems"
                  :max="localCard.checklist.length"
                ></progress>
              </div>
              
              <div class="checklist-items">
                <div
                  v-for="item in localCard.checklist"
                  :key="item.id"
                  class="checklist-item"
                >
                  <label class="checkbox">
                    <input
                      v-model="item.completed"
                      type="checkbox"
                      @change="updateChecklistItem(item)"
                    />
                    <span
                      :class="{ 'completed': item.completed }"
                      class="checklist-text"
                    >
                      {{ item.text }}
                    </span>
                  </label>
                  <button
                    @click="removeChecklistItem(item.id)"
                    class="button is-small is-ghost has-text-danger"
                  >
                    <i class="fas fa-trash"></i>
                  </button>
                </div>
              </div>
              
              <div v-if="showChecklistEditor" class="checklist-editor">
                <div class="field has-addons">
                  <div class="control is-expanded">
                    <input
                      v-model="newChecklistItem"
                      @keydown.enter="saveChecklistItem"
                      @keydown.escape="cancelChecklistEdit"
                      class="input is-small"
                      placeholder="Add checklist item"
                      ref="checklistInput"
                    />
                  </div>
                  <div class="control">
                    <button
                      @click="saveChecklistItem"
                      class="button is-small is-primary"
                      :disabled="!newChecklistItem.trim()"
                    >
                      Add
                    </button>
                  </div>
                  <div class="control">
                    <button
                      @click="cancelChecklistEdit"
                      class="button is-small"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Comments -->
            <div class="detail-section">
              <div class="detail-header">
                <h4 class="detail-title">
                  <i class="fas fa-comment"></i>
                  Comments
                </h4>
              </div>
              
              <div class="comment-editor">
                <div class="field">
                  <div class="control">
                    <textarea
                      v-model="newComment"
                      class="textarea is-small"
                      placeholder="Write a comment..."
                      rows="2"
                    ></textarea>
                  </div>
                </div>
                <div class="field">
                  <div class="control">
                    <button
                      @click="addComment"
                      class="button is-primary is-small"
                      :disabled="!newComment.trim()"
                    >
                      Comment
                    </button>
                  </div>
                </div>
              </div>
              
              <div class="comments-list">
                <div
                  v-for="comment in localCard.comments"
                  :key="comment.id"
                  class="comment"
                >
                  <div class="comment-header">
                    <strong>{{ comment.authorName }}</strong>
                    <span class="comment-date">{{ formatDate(comment.createdAt) }}</span>
                  </div>
                  <div class="comment-text">{{ comment.text }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Sidebar -->
          <div class="sidebar-content">
            <!-- Due Date -->
            <div class="sidebar-section">
              <h5 class="sidebar-title">Due Date</h5>
              <div v-if="localCard.dueDate" class="due-date-display">
                <span
                  class="tag"
                  :class="dueDateClass"
                >
                  <i class="fas fa-clock"></i>
                  {{ formatDueDate(localCard.dueDate) }}
                </span>
                <button
                  @click="removeDueDate"
                  class="button is-small is-light"
                >
                  Remove
                </button>
              </div>
              <div v-else>
                <input
                  v-model="dueDateInput"
                  type="datetime-local"
                  class="input is-small"
                  @change="setDueDate"
                />
              </div>
            </div>

            <!-- Priority -->
            <div class="sidebar-section">
              <h5 class="sidebar-title">Priority</h5>
              <div class="field">
                <div class="control">
                  <div class="select is-small is-fullwidth">
                    <select v-model="localCard.priority" @change="updateCard">
                      <option value="low">Low</option>
                      <option value="medium">Medium</option>
                      <option value="high">High</option>
                      <option value="urgent">Urgent</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>

            <!-- Assignees -->
            <div class="sidebar-section">
              <h5 class="sidebar-title">Assignees</h5>
              <div class="assignees-list">
                <div
                  v-for="assignee in localCard.assignees"
                  :key="assignee.id"
                  class="assignee-item"
                >
                  <div class="assignee-avatar">
                    <img
                      v-if="assignee.avatar"
                      :src="assignee.avatar"
                      :alt="assignee.name"
                      class="avatar"
                    />
                    <span v-else class="avatar-placeholder">
                      {{ assignee.name.charAt(0).toUpperCase() }}
                    </span>
                  </div>
                  <span class="assignee-name">{{ assignee.name }}</span>
                  <button
                    @click="removeAssignee(assignee.id)"
                    class="button is-small is-ghost has-text-danger"
                  >
                    <i class="fas fa-times"></i>
                  </button>
                </div>
              </div>
              <button
                @click="showAssigneeSelector = true"
                class="button is-small is-light is-fullwidth"
              >
                <i class="fas fa-plus"></i>
                <span>Add Assignee</span>
              </button>
            </div>

            <!-- Attachments -->
            <div class="sidebar-section">
              <h5 class="sidebar-title">Attachments</h5>
              <div class="attachments-list">
                <div
                  v-for="attachment in localCard.attachments"
                  :key="attachment.id"
                  class="attachment-item"
                >
                  <div class="attachment-info">
                    <i class="fas fa-paperclip"></i>
                    <span class="attachment-name">{{ attachment.name }}</span>
                    <span class="attachment-size">({{ formatFileSize(attachment.size) }})</span>
                  </div>
                  <div class="attachment-actions">
                    <a
                      :href="attachment.url"
                      target="_blank"
                      class="button is-small is-light"
                    >
                      <i class="fas fa-external-link-alt"></i>
                    </a>
                    <button
                      @click="removeAttachment(attachment.id)"
                      class="button is-small is-ghost has-text-danger"
                    >
                      <i class="fas fa-trash"></i>
                    </button>
                  </div>
                </div>
              </div>
              <div class="file-upload">
                <input
                  ref="fileInput"
                  type="file"
                  multiple
                  @change="handleFileUpload"
                  style="display: none"
                />
                <button
                  @click="triggerFilePicker"
                  class="button is-small is-light is-fullwidth"
                >
                  <i class="fas fa-plus"></i>
                  <span>Add Attachment</span>
                </button>
              </div>
            </div>

            <!-- Actions -->
            <div class="sidebar-section">
              <h5 class="sidebar-title">Actions</h5>
              <div class="action-buttons">
                <button
                  @click="duplicateCard"
                  class="button is-small is-light is-fullwidth"
                >
                  <i class="fas fa-copy"></i>
                  <span>Duplicate</span>
                </button>
                <button
                  @click="archiveCard"
                  class="button is-small is-light is-fullwidth"
                >
                  <i class="fas fa-archive"></i>
                  <span>Archive</span>
                </button>
                <button
                  @click="deleteCard"
                  class="button is-small is-danger is-fullwidth"
                >
                  <i class="fas fa-trash"></i>
                  <span>Delete</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>

    <!-- Assignee Selector Modal -->
    <div v-if="showAssigneeSelector" class="modal is-active">
      <div class="modal-background" @click="showAssigneeSelector = false"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">Add Assignee</p>
          <button
            class="delete"
            @click="showAssigneeSelector = false"
            aria-label="close"
          ></button>
        </header>
        <section class="modal-card-body">
          <div class="field">
            <label class="label">Email</label>
            <div class="control">
              <input
                v-model="newAssigneeEmail"
                class="input"
                type="email"
                placeholder="Enter email address"
              />
            </div>
          </div>
          <div class="field">
            <label class="label">Name</label>
            <div class="control">
              <input
                v-model="newAssigneeName"
                class="input"
                type="text"
                placeholder="Enter name"
              />
            </div>
          </div>
        </section>
        <footer class="modal-card-foot">
          <button
            @click="addAssignee"
            class="button is-primary"
            :disabled="!newAssigneeEmail.trim() || !newAssigneeName.trim()"
          >
            Add
          </button>
          <button @click="showAssigneeSelector = false" class="button">
            Cancel
          </button>
        </footer>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, watch } from 'vue'
import type { 
  KanbanCard, 
  KanbanLabel, 
  KanbanAssignee, 
  ChecklistItem, 
  Comment, 
  Attachment 
} from '../../types/kanban'

interface Props {
  card: KanbanCard
  columnTitle: string
  boardMembers: KanbanAssignee[]
}

interface Emits {
  (e: 'close'): void
  (e: 'update', card: KanbanCard): void
  (e: 'delete', cardId: string): void
  (e: 'duplicate', card: KanbanCard): void
  (e: 'archive', cardId: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Refs
const titleInput = ref<HTMLInputElement>()
const descriptionTextarea = ref<HTMLTextAreaElement>()
const checklistInput = ref<HTMLInputElement>()
const fileInput = ref<HTMLInputElement>()
const triggerFilePicker = () => {
  fileInput.value?.click()
}

// Local state
const localCard = ref<KanbanCard>({ ...props.card })
const isEditingTitle = ref(false)
const isEditingDescription = ref(false)
const showLabelEditor = ref(false)
const showChecklistEditor = ref(false)
const showAssigneeSelector = ref(false)

// Form data
const newLabel = ref({ name: '', color: '#3273dc' })
const newChecklistItem = ref('')
const newComment = ref('')
const dueDateInput = ref('')
const newAssigneeEmail = ref('')
const newAssigneeName = ref('')

// Computed
const completedChecklistItems = computed(() => {
  return localCard.value.checklist.filter(item => item.completed).length
})

const dueDateClass = computed(() => {
  if (!localCard.value.dueDate) return ''
  
  const dueDate = new Date(localCard.value.dueDate)
  const now = new Date()
  const diffDays = Math.ceil((dueDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
  
  if (diffDays < 0) return 'is-danger' // Overdue
  if (diffDays === 0) return 'is-warning' // Due today
  if (diffDays <= 3) return 'is-info' // Due soon
  return 'is-success' // Due later
})

// Utility functions
const generateId = () => Math.random().toString(36).substr(2, 9)

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatDueDate = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffDays = Math.ceil((date.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
  
  if (diffDays < 0) return `Overdue by ${Math.abs(diffDays)} days`
  if (diffDays === 0) return 'Due today'
  if (diffDays === 1) return 'Due tomorrow'
  return `Due in ${diffDays} days`
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// Title editing
const startTitleEdit = () => {
  isEditingTitle.value = true
  nextTick(() => {
    titleInput.value?.focus()
    titleInput.value?.select()
  })
}

const saveTitle = () => {
  if (localCard.value.title.trim()) {
    isEditingTitle.value = false
    updateCard()
  }
}

const cancelTitleEdit = () => {
  localCard.value.title = props.card.title
  isEditingTitle.value = false
}

// Description editing
const startDescriptionEdit = () => {
  isEditingDescription.value = true
  nextTick(() => {
    descriptionTextarea.value?.focus()
  })
}

const saveDescription = () => {
  isEditingDescription.value = false
  updateCard()
}

const cancelDescriptionEdit = () => {
  localCard.value.description = props.card.description
  isEditingDescription.value = false
}

// Label management
const addLabel = () => {
  if (!newLabel.value.name.trim()) return
  
  const label: KanbanLabel = {
    id: generateId(),
    name: newLabel.value.name.trim(),
    color: newLabel.value.color
  }
  
  localCard.value.labels.push(label)
  newLabel.value = { name: '', color: '#3273dc' }
  showLabelEditor.value = false
  updateCard()
}

const removeLabel = (labelId: string) => {
  localCard.value.labels = localCard.value.labels.filter(l => l.id !== labelId)
  updateCard()
}

const cancelLabelEdit = () => {
  newLabel.value = { name: '', color: '#3273dc' }
  showLabelEditor.value = false
}

// Due date management
const setDueDate = () => {
  if (dueDateInput.value) {
    localCard.value.dueDate = new Date(dueDateInput.value).toISOString()
    updateCard()
  }
}

const removeDueDate = () => {
  localCard.value.dueDate = undefined
  updateCard()
}

// Checklist management
const addChecklistItem = () => {
  showChecklistEditor.value = true
  nextTick(() => {
    checklistInput.value?.focus()
  })
}

const saveChecklistItem = () => {
  if (!newChecklistItem.value.trim()) return
  
  const item: ChecklistItem = {
    id: generateId(),
    text: newChecklistItem.value.trim(),
    completed: false,
    createdAt: new Date().toISOString()
  }
  
  localCard.value.checklist.push(item)
  newChecklistItem.value = ''
  showChecklistEditor.value = false
  updateCard()
}

const updateChecklistItem = (item: ChecklistItem) => {
  updateCard()
}

const removeChecklistItem = (itemId: string) => {
  localCard.value.checklist = localCard.value.checklist.filter(i => i.id !== itemId)
  updateCard()
}

const cancelChecklistEdit = () => {
  newChecklistItem.value = ''
  showChecklistEditor.value = false
}

// Comment management
const addComment = () => {
  if (!newComment.value.trim()) return
  
  const comment: Comment = {
    id: generateId(),
    text: newComment.value.trim(),
    authorId: 'current-user', // TODO: Get from auth store
    authorName: 'Current User', // TODO: Get from auth store
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
  
  localCard.value.comments.push(comment)
  newComment.value = ''
  updateCard()
}

// Assignee management
const addAssignee = () => {
  if (!newAssigneeEmail.value.trim() || !newAssigneeName.value.trim()) return
  
  const assignee: KanbanAssignee = {
    id: generateId(),
    name: newAssigneeName.value.trim(),
    email: newAssigneeEmail.value.trim()
  }
  
  localCard.value.assignees.push(assignee)
  newAssigneeEmail.value = ''
  newAssigneeName.value = ''
  showAssigneeSelector.value = false
  updateCard()
}

const removeAssignee = (assigneeId: string) => {
  localCard.value.assignees = localCard.value.assignees.filter(a => a.id !== assigneeId)
  updateCard()
}

// Attachment management
const handleFileUpload = (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = target.files
  if (!files) return
  
  Array.from(files).forEach(file => {
    // In a real app, you would upload the file to a server
    // For now, we'll create a mock attachment
    const attachment: Attachment = {
      id: generateId(),
      name: file.name,
      url: URL.createObjectURL(file), // Mock URL
      type: file.type,
      size: file.size,
      uploadedAt: new Date().toISOString()
    }
    
    localCard.value.attachments.push(attachment)
  })
  
  updateCard()
  target.value = '' // Reset file input
}

const removeAttachment = (attachmentId: string) => {
  localCard.value.attachments = localCard.value.attachments.filter(a => a.id !== attachmentId)
  updateCard()
}

// Card actions
const duplicateCard = () => {
  emit('duplicate', localCard.value)
  emit('close')
}

const archiveCard = () => {
  emit('archive', localCard.value.id)
  emit('close')
}

const deleteCard = () => {
  if (confirm('Are you sure you want to delete this card?')) {
    emit('delete', localCard.value.id)
    emit('close')
  }
}

// Update card
const updateCard = () => {
  localCard.value.updatedAt = new Date().toISOString()
  emit('update', localCard.value)
}

// Watch for external changes
watch(() => props.card, (newCard) => {
  localCard.value = { ...newCard }
}, { deep: true })

// Initialize due date input
watch(() => localCard.value.dueDate, (newDueDate) => {
  if (newDueDate) {
    dueDateInput.value = new Date(newDueDate).toISOString().slice(0, 16)
  } else {
    dueDateInput.value = ''
  }
}, { immediate: true })
</script>

<style scoped>
.card-details-modal {
  width: 900px;
  max-width: 95vw;
  max-height: 90vh;
}

.card-header-content {
  flex: 1;
}

.card-title-section {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

.card-icon {
  color: #666;
  font-size: 1.2rem;
}

.editable-title {
  cursor: pointer;
  margin: 0;
}

.editable-title:hover {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  padding: 0.25rem;
  margin: -0.25rem;
}

.title-input {
  font-size: 1.25rem;
  font-weight: 600;
  border: none;
  box-shadow: none;
  padding: 0.25rem;
}

.card-meta {
  color: #666;
  font-size: 0.9rem;
}

.column-name {
  font-weight: 500;
}

.modal-card-body {
  padding: 1.5rem;
  max-height: calc(90vh - 120px);
  overflow-y: auto;
}

.card-details-grid {
  display: grid;
  grid-template-columns: 1fr 280px;
  gap: 2rem;
}

.main-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.sidebar-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.detail-section {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 1rem;
}

.detail-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.detail-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.75rem;
}

.detail-title {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.sidebar-title {
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #666;
}

.sidebar-section {
  background: #f9f9f9;
  padding: 1rem;
  border-radius: 6px;
}

/* Labels */
.labels-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  align-items: center;
}

.label-tag {
  color: white;
  font-weight: 500;
  position: relative;
}

.label-editor {
  margin-top: 0.75rem;
}

.color-input {
  width: 50px;
  padding: 0.25rem;
}

/* Description */
.description-editor textarea {
  resize: vertical;
}

.description-display {
  background: #f9f9f9;
  padding: 0.75rem;
  border-radius: 4px;
  cursor: pointer;
  white-space: pre-wrap;
  line-height: 1.5;
}

.description-display:hover {
  background: #f0f0f0;
}

.description-placeholder {
  background: #f9f9f9;
  padding: 0.75rem;
  border-radius: 4px;
  cursor: pointer;
  color: #999;
  font-style: italic;
}

.description-placeholder:hover {
  background: #f0f0f0;
}

/* Checklist */
.checklist-progress {
  font-size: 0.8rem;
  color: #666;
  font-weight: normal;
}

.checklist-progress-bar {
  margin-bottom: 0.75rem;
}

.checklist-items {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.checklist-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: white;
  border-radius: 4px;
}

.checklist-text {
  flex: 1;
  transition: all 0.2s ease;
}

.checklist-text.completed {
  text-decoration: line-through;
  color: #999;
}

/* Comments */
.comment-editor {
  margin-bottom: 1rem;
}

.comments-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.comment {
  background: #f9f9f9;
  padding: 0.75rem;
  border-radius: 4px;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.25rem;
}

.comment-date {
  font-size: 0.8rem;
  color: #666;
}

.comment-text {
  line-height: 1.4;
}

/* Due Date */
.due-date-display {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.due-date-display .tag {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* Assignees */
.assignees-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.assignee-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: white;
  border-radius: 4px;
}

.assignee-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: #3273dc;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
}

.assignee-name {
  flex: 1;
  font-size: 0.9rem;
}

/* Attachments */
.attachments-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.attachment-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem;
  background: white;
  border-radius: 4px;
}

.attachment-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
}

.attachment-name {
  font-size: 0.9rem;
  font-weight: 500;
}

.attachment-size {
  font-size: 0.8rem;
  color: #666;
}

.attachment-actions {
  display: flex;
  gap: 0.25rem;
}

/* Actions */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* Responsive */
@media (max-width: 768px) {
  .card-details-modal {
    width: 100%;
    height: 100%;
    max-width: none;
    max-height: none;
  }
  
  .card-details-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .modal-card-body {
    padding: 1rem;
  }
}
</style>