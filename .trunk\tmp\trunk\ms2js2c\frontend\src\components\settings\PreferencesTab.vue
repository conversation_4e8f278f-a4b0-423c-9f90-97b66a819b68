<template>
  <div class="preferences-tab">
    <div class="notification is-info is-light" v-if="successMessage">
      <button class="delete" @click="successMessage = ''"></button>
      {{ successMessage }}
    </div>

    <div class="notification is-danger is-light" v-if="errorMessage">
      <button class="delete" @click="errorMessage = ''"></button>
      {{ errorMessage }}
    </div>

    <!-- Theme Settings -->
    <div class="field-group">
      <h3 class="subtitle is-5">
        <span class="icon">
          <i class="fas fa-palette"></i>
        </span>
        Appearance
      </h3>
      
      <div class="field">
        <label class="label">Theme</label>
        <div class="control">
          <div class="select is-fullwidth">
            <select 
              v-model="localPreferences.theme" 
              @change="updateTheme"
              :disabled="isLoading"
            >
              <option value="light">Light</option>
              <option value="dark">Dark</option>
              <option value="auto">Auto (System)</option>
            </select>
          </div>
        </div>
        <p class="help">Choose your preferred color scheme</p>
      </div>
    </div>

    <!-- Language Settings -->
    <div class="field-group">
      <h3 class="subtitle is-5">
        <span class="icon">
          <i class="fas fa-globe"></i>
        </span>
        Language & Region
      </h3>
      
      <div class="field">
        <label class="label">Language</label>
        <div class="control">
          <div class="select is-fullwidth">
            <select 
              v-model="localPreferences.language" 
              @change="updateLanguage"
              :disabled="isLoading"
            >
              <option value="en">English</option>
              <option value="es">Español</option>
              <option value="fr">Français</option>
              <option value="de">Deutsch</option>
              <option value="it">Italiano</option>
              <option value="pt">Português</option>
              <option value="ja">日本語</option>
              <option value="ko">한국어</option>
              <option value="zh">中文</option>
            </select>
          </div>
        </div>
        <p class="help">Select your preferred language</p>
      </div>

      <div class="field">
        <label class="label">Timezone</label>
        <div class="control">
          <input 
            class="input" 
            type="text" 
            v-model="localPreferences.timezone"
            @blur="updateTimezone"
            :disabled="isLoading"
            placeholder="UTC"
          >
        </div>
        <p class="help">Your timezone (e.g., America/New_York, Europe/London)</p>
      </div>
    </div>

    <!-- Editor Settings -->
    <div class="field-group">
      <h3 class="subtitle is-5">
        <span class="icon">
          <i class="fas fa-edit"></i>
        </span>
        Editor
      </h3>
      
      <div class="field">
        <label class="label">Auto-save Interval</label>
        <div class="control">
          <div class="select is-fullwidth">
            <select 
              v-model="localPreferences.autoSaveInterval" 
              @change="updateAutoSaveInterval"
              :disabled="isLoading"
            >
              <option :value="5000">5 seconds</option>
              <option :value="10000">10 seconds</option>
              <option :value="30000">30 seconds</option>
              <option :value="60000">1 minute</option>
              <option :value="120000">2 minutes</option>
              <option :value="300000">5 minutes</option>
            </select>
          </div>
        </div>
        <p class="help">How often to automatically save your notes</p>
      </div>
    </div>

    <!-- Notification Settings -->
    <div class="field-group">
      <h3 class="subtitle is-5">
        <span class="icon">
          <i class="fas fa-bell"></i>
        </span>
        Notifications
      </h3>
      
      <div class="field">
        <div class="control">
          <label class="checkbox">
            <input 
              type="checkbox" 
              v-model="localPreferences.notifications.email"
              @change="updateNotifications"
              :disabled="isLoading"
            >
            Email notifications
          </label>
        </div>
        <p class="help">Receive notifications via email</p>
      </div>

      <div class="field">
        <div class="control">
          <label class="checkbox">
            <input 
              type="checkbox" 
              v-model="localPreferences.notifications.push"
              @change="updateNotifications"
              :disabled="isLoading"
            >
            Push notifications
          </label>
        </div>
        <p class="help">Receive browser push notifications</p>
      </div>

      <div class="field">
        <div class="control">
          <label class="checkbox">
            <input 
              type="checkbox" 
              v-model="localPreferences.notifications.mentions"
              @change="updateNotifications"
              :disabled="isLoading"
            >
            Mention notifications
          </label>
        </div>
        <p class="help">Get notified when someone mentions you</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { useSettingsStore } from '../../stores/settings'
import { useAuthStore } from '../../stores/auth'
import type { UserPreferences } from '../../services/userService'

const settingsStore = useSettingsStore()
const authStore = useAuthStore()
const isLoading = ref(false)
const successMessage = ref('')
const errorMessage = ref('')

const localPreferences = reactive<UserPreferences>({
  theme: 'auto',
  language: 'en',
  timezone: 'UTC',
  autoSaveInterval: 30000,
  notifications: {
    email: true,
    push: true,
    mentions: true
  }
})

// Watch for changes in the store and update local preferences
watch(() => settingsStore.preferences, (newPreferences) => {
  Object.assign(localPreferences, newPreferences)
}, { deep: true, immediate: true })

const showSuccess = (message: string) => {
  successMessage.value = message
  errorMessage.value = ''
  setTimeout(() => {
    successMessage.value = ''
  }, 3000)
}

const showError = (message: string) => {
  errorMessage.value = message
  successMessage.value = ''
}

const updateTheme = async () => {
  isLoading.value = true
  try {
    const result = await settingsStore.updateTheme(localPreferences.theme)
    if (result.success) {
      // Save theme preference to authStore for immediate application
      authStore.saveThemePreference(localPreferences.theme)
      showSuccess('Theme updated successfully')
    } else {
      showError(result.error || 'Failed to update theme')
    }
  } catch (error) {
    showError('Failed to update theme')
  } finally {
    isLoading.value = false
  }
}

const updateLanguage = async () => {
  isLoading.value = true
  try {
    const result = await settingsStore.updateLanguage(localPreferences.language)
    if (result.success) {
      showSuccess('Language updated successfully')
    } else {
      showError(result.error || 'Failed to update language')
    }
  } catch (error) {
    showError('Failed to update language')
  } finally {
    isLoading.value = false
  }
}

const updateTimezone = async () => {
  isLoading.value = true
  try {
    const result = await settingsStore.updateSettings({ timezone: localPreferences.timezone })
    if (result.success) {
      showSuccess('Timezone updated successfully')
    } else {
      showError(result.error || 'Failed to update timezone')
    }
  } catch (error) {
    showError('Failed to update timezone')
  } finally {
    isLoading.value = false
  }
}

const updateAutoSaveInterval = async () => {
  isLoading.value = true
  try {
    const result = await settingsStore.updateAutoSaveInterval(localPreferences.autoSaveInterval)
    if (result.success) {
      showSuccess('Auto-save interval updated successfully')
    } else {
      showError(result.error || 'Failed to update auto-save interval')
    }
  } catch (error) {
    showError('Failed to update auto-save interval')
  } finally {
    isLoading.value = false
  }
}

const updateNotifications = async () => {
  isLoading.value = true
  try {
    const result = await settingsStore.updateNotifications(localPreferences.notifications)
    if (result.success) {
      showSuccess('Notification preferences updated successfully')
    } else {
      showError(result.error || 'Failed to update notification preferences')
    }
  } catch (error) {
    showError('Failed to update notification preferences')
  } finally {
    isLoading.value = false
  }
}

onMounted(() => {
  if (!settingsStore.preferences.theme) {
    settingsStore.loadSettings()
  }
})
</script>

<style scoped>
.preferences-tab {
  max-width: 600px;
}

.field-group {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #e5e5e5;
}

.field-group:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.subtitle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  color: #363636;
}

.field {
  margin-bottom: 1rem;
}

.label {
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.help {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.checkbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: normal;
}

.notification {
  margin-bottom: 1rem;
}

/* Dark mode styles */
:global(.dark) .field-group {
  border-bottom-color: #404040;
}

:global(.dark) .subtitle {
  color: #e5e5e5;
}

:global(.dark) .help {
  color: #9ca3af;
}

:global(.dark) .input,
:global(.dark) .select select {
  background: #374151;
  border-color: #4b5563;
  color: #e5e5e5;
}

:global(.dark) .input:focus,
:global(.dark) .select select:focus {
  border-color: #6366f1;
  box-shadow: 0 0 0 0.125em rgba(99, 102, 241, 0.25);
}

:global(.dark) .label {
  color: #e5e5e5;
}
</style>