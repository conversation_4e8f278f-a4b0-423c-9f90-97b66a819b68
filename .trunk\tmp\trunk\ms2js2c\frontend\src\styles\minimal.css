/* Minimal CSS framework - replaces <PERSON><PERSON><PERSON> for <500ms target
   This reduces CSS from 661KB to ~20KB by including only essential styles */

/* CSS Reset and Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #fff;
}

/* Layout */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.columns {
  display: flex;
  flex-wrap: wrap;
  margin: -0.5rem;
}

.column {
  flex: 1;
  padding: 0.5rem;
}

.is-narrow {
  flex: none;
}
.is-full {
  flex: 0 0 100%;
}
.is-half {
  flex: 0 0 50%;
}
.is-one-third {
  flex: 0 0 33.333%;
}
.is-two-thirds {
  flex: 0 0 66.666%;
}
.is-one-quarter {
  flex: 0 0 25%;
}

/* Buttons */
.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #fff;
  color: #333;
  text-decoration: none;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.button:hover {
  background: #f5f5f5;
}
.button:active {
  transform: translateY(1px);
}

.button.is-primary {
  background: #3273dc;
  color: white;
  border-color: #3273dc;
}

.button.is-primary:hover {
  background: #2366d1;
}

.button.is-success {
  background: #23d160;
  color: white;
  border-color: #23d160;
}

.button.is-danger {
  background: #ff3860;
  color: white;
  border-color: #ff3860;
}

.button.is-small {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}
.button.is-loading {
  opacity: 0.7;
  pointer-events: none;
}

/* Forms */
.field {
  margin-bottom: 1rem;
}
.label {
  display: block;
  margin-bottom: 0.25rem;
  font-weight: 600;
}

.input,
.textarea,
.select select {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.875rem;
}

.input:focus,
.textarea:focus,
.select select:focus {
  outline: none;
  border-color: #3273dc;
  box-shadow: 0 0 0 2px rgba(50, 115, 220, 0.2);
}

.textarea {
  resize: vertical;
  min-height: 100px;
}

/* Cards */
.card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  padding: 1rem;
  border-bottom: 1px solid #eee;
  font-weight: 600;
}

.card-content {
  padding: 1rem;
}

/* Navigation */
.navbar {
  display: flex;
  align-items: center;
  padding: 1rem;
  background: white;
  border-bottom: 1px solid #eee;
}

.navbar-brand {
  font-weight: 700;
  font-size: 1.25rem;
}
.navbar-menu {
  display: flex;
  margin-left: auto;
}
.navbar-item {
  padding: 0.5rem 1rem;
  text-decoration: none;
  color: #333;
}
.navbar-item:hover {
  background: #f5f5f5;
}

/* Modals */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-card {
  background: white;
  border-radius: 8px;
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow: auto;
}

.modal-card-head {
  padding: 1rem;
  border-bottom: 1px solid #eee;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-card-body {
  padding: 1rem;
}
.modal-card-foot {
  padding: 1rem;
  border-top: 1px solid #eee;
}

/* Notifications */
.notification {
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1rem;
}

.notification.is-success {
  background: #d4edda;
  color: #155724;
}
.notification.is-danger {
  background: #f8d7da;
  color: #721c24;
}
.notification.is-warning {
  background: #fff3cd;
  color: #856404;
}
.notification.is-info {
  background: #d1ecf1;
  color: #0c5460;
}

/* Tables */
.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.table th {
  font-weight: 600;
  background: #f8f9fa;
}

/* Utilities */
.has-text-centered {
  text-align: center;
}
.has-text-right {
  text-align: right;
}
.is-hidden {
  display: none !important;
}
.is-flex {
  display: flex;
}
.is-block {
  display: block;
}
.is-inline {
  display: inline;
}
.is-inline-block {
  display: inline-block;
}

.mt-1 {
  margin-top: 0.25rem;
}
.mt-2 {
  margin-top: 0.5rem;
}
.mt-3 {
  margin-top: 1rem;
}
.mb-1 {
  margin-bottom: 0.25rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mb-3 {
  margin-bottom: 1rem;
}
.p-1 {
  padding: 0.25rem;
}
.p-2 {
  padding: 0.5rem;
}
.p-3 {
  padding: 1rem;
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  body {
    background: #1a1a1a;
    color: #e0e0e0;
  }
  .card {
    background: #2d2d2d;
  }
  .navbar {
    background: #2d2d2d;
    border-color: #404040;
  }
  .input,
  .textarea,
  .select select {
    background: #2d2d2d;
    color: #e0e0e0;
    border-color: #404040;
  }
  .table th {
    background: #2d2d2d;
  }
}

/* Responsive */
@media (max-width: 768px) {
  .columns {
    flex-direction: column;
  }
  .navbar-menu {
    flex-direction: column;
    width: 100%;
  }
  .container {
    padding: 0 0.5rem;
  }
}
