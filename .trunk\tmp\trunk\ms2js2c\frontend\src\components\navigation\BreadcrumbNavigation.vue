<template>
  <nav class="breadcrumb" aria-label="breadcrumbs" v-if="isRouteReady">
    <ul>
      <li v-for="(crumb, index) in breadcrumbs" :key="crumb.path" :class="{ 'is-active': index === breadcrumbs.length - 1 }">
        <router-link 
          v-if="index < breadcrumbs.length - 1" 
          :to="crumb.path"
          class="breadcrumb-link"
        >
          <span v-if="crumb.icon" class="icon is-small">
            <i :class="crumb.icon"></i>
          </span>
          <span>{{ crumb.name }}</span>
        </router-link>
        <span v-else class="breadcrumb-current">
          <span v-if="crumb.icon" class="icon is-small">
            <i :class="crumb.icon"></i>
          </span>
          <span>{{ crumb.name }}</span>
        </span>
      </li>
    </ul>
  </nav>
  <div v-else class="breadcrumb-placeholder">
    <div class="breadcrumb-skeleton"></div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'

interface BreadcrumbItem {
  name: string
  path: string
  icon?: string
}

// Props
interface Props {
  customBreadcrumbs?: BreadcrumbItem[]
}

const props = defineProps<Props>()

// Composables
const route = useRoute()

// Check if route is ready
const isRouteReady = computed(() => {
  return !!(route && route.name && route.path)
})

// Computed breadcrumbs
const breadcrumbs = computed(() => {
  if (props.customBreadcrumbs) {
    return props.customBreadcrumbs
  }

  const crumbs: BreadcrumbItem[] = []
  
  // Always start with Dashboard
  crumbs.push({
    name: 'Dashboard',
    path: '/dashboard',
    icon: 'fas fa-home'
  })

  // Guard against undefined route during initialization
  if (!route || !route.name || !route.path) {
    // Return just dashboard if route is not ready
    return crumbs
  }

  // Add route-specific breadcrumbs
  const routeName = route.name as string
  const routePath = route.path

  switch (routeName) {
    case 'Dashboard':
      // Dashboard is already added, no additional crumbs needed
      break
    
    case 'Notes':
      crumbs.push({
        name: 'All Notes',
        path: '/dashboard/notes',
        icon: 'fas fa-sticky-note'
      })
      break
    
    case 'RecentNotes':
      crumbs.push({
        name: 'Recent Notes',
        path: '/dashboard/recent',
        icon: 'fas fa-clock'
      })
      break
    
    case 'FavoriteNotes':
      crumbs.push({
        name: 'Favorites',
        path: '/dashboard/favorites',
        icon: 'fas fa-star'
      })
      break
    
    case 'ArchivedNotes':
      crumbs.push({
        name: 'Archived',
        path: '/dashboard/archived',
        icon: 'fas fa-archive'
      })
      break
    
    case 'Note':
      crumbs.push({
        name: 'Notes',
        path: '/dashboard/notes',
        icon: 'fas fa-sticky-note'
      })
      
      if (route.params && route.params.id) {
        crumbs.push({
          name: (route.meta && route.meta.noteTitle as string) || 'Note',
          path: routePath,
          icon: 'fas fa-file-alt'
        })
      }
      break
    
    case 'Search':
      crumbs.push({
        name: 'Search Results',
        path: routePath,
        icon: 'fas fa-search'
      })
      break
    
    case 'Settings':
      crumbs.push({
        name: 'Settings',
        path: '/dashboard/settings',
        icon: 'fas fa-cog'
      })
      break
    
    case 'Group':
      crumbs.push({
        name: 'Groups',
        path: '/dashboard/groups',
        icon: 'fas fa-users'
      })
      
      if (route.params && route.params.id) {
        crumbs.push({
          name: (route.meta && route.meta.groupName as string) || 'Group',
          path: routePath,
          icon: 'fas fa-users'
        })
      }
      break
    
    case 'SharedNotes':
      crumbs.push({
        name: 'Shared Notes',
        path: '/dashboard/shared',
        icon: 'fas fa-share-alt'
      })
      break
    
    case 'SharedNote':
      crumbs.push({
        name: 'Shared Notes',
        path: '/dashboard/shared',
        icon: 'fas fa-share-alt'
      })
      
      if (route.params && route.params.id) {
        crumbs.push({
          name: (route.meta && route.meta.noteTitle as string) || 'Shared Note',
          path: routePath,
          icon: 'fas fa-file-alt'
        })
      }
      break
    
    default:
      // For unknown routes, try to build breadcrumbs from path segments
      if (routePath) {
        const segments = routePath.split('/').filter(segment => segment && segment !== 'dashboard')
        segments.forEach((segment, index) => {
          const segmentPath = '/dashboard/' + segments.slice(0, index + 1).join('/')
          crumbs.push({
            name: segment.charAt(0).toUpperCase() + segment.slice(1),
            path: segmentPath
          })
        })
      }
      break
  }

  return crumbs
})
</script>

<style scoped>
.breadcrumb {
  padding: 0.75rem 1rem;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  margin: 0;
}

.breadcrumb ul {
  margin: 0;
}

.breadcrumb-link {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #007bff;
  text-decoration: none;
  transition: color 0.2s ease;
}

.breadcrumb-link:hover {
  color: #0056b3;
}

.breadcrumb-placeholder {
  padding: 0.75rem 1rem;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  margin: 0;
}

.breadcrumb-skeleton {
  height: 1.5rem;
  background: linear-gradient(90deg, #e9ecef 25%, #f8f9fa 50%, #e9ecef 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 0.25rem;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.breadcrumb-current {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #6c757d;
  font-weight: 500;
}

.breadcrumb li.is-active .breadcrumb-current {
  color: #363636;
}

/* Responsive */
@media screen and (max-width: 768px) {
  .breadcrumb {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
  }
  
  .breadcrumb ul {
    flex-wrap: wrap;
  }
  
  .breadcrumb li {
    margin-bottom: 0.25rem;
  }
}
</style>