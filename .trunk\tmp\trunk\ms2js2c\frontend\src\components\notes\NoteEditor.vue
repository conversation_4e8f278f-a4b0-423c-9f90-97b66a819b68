<template>
  <div class="note-editor">
    <!-- Auto-save status indicator -->
    <div class="auto-save-status" v-if="showAutoSaveStatus">
      <span 
        class="status-indicator"
        :class="{
          'is-saving': notesStore.autoSaveStatus === 'saving',
          'is-saved': notesStore.autoSaveStatus === 'saved',
          'is-error': notesStore.autoSaveStatus === 'error'
        }"
      >
        <template v-if="notesStore.autoSaveStatus === 'saving'">
          <i class="fas fa-spinner fa-spin"></i> Saving...
        </template>
        <template v-else-if="notesStore.autoSaveStatus === 'saved'">
          <i class="fas fa-check"></i> Saved
        </template>
        <template v-else-if="notesStore.autoSaveStatus === 'error'">
          <i class="fas fa-exclamation-triangle"></i> Save failed
        </template>
      </span>
    </div>

    <!-- Offline indicator -->
    <div class="offline-indicator" v-if="!offlineSync.isOnline.value">
      <span class="tag is-warning">
        <i class="fas fa-wifi-slash"></i>
        Offline
        <span v-if="offlineSync.queuedOperations.value > 0">
          ({{ offlineSync.queuedOperations.value }} pending)
        </span>
      </span>
    </div>

    <!-- Note title -->
    <div class="field">
      <div class="control">
        <input
          v-model="localTitle"
          @input="onTitleChange"
          class="input is-large"
          type="text"
          placeholder="Note title..."
          :disabled="!note && !isCreating"
        />
      </div>
    </div>

    <!-- Note type selector -->
    <div class="field" v-if="!note">
      <label class="label">Note Type</label>
      <div class="control">
        <div class="select">
          <select v-model="selectedNoteType">
            <option value="markdown">Markdown</option>
            <option value="richtext">Rich Text</option>
            <option value="kanban">Kanban Board</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Content editor based on note type -->
    <div class="editor-container">
      <!-- Markdown Editor -->
      <div v-if="currentNoteType === 'markdown'" class="markdown-editor">
        <MarkdownEditor
          v-model="localContent"
          @change="onContentChange"
          :disabled="!note && !isCreating"
          placeholder="Start writing your markdown..."
        />
      </div>

      <!-- Rich Text Editor -->
      <div v-else-if="currentNoteType === 'richtext'" class="richtext-editor">
        <RichTextEditor
          v-model="localContent"
          @change="onContentChange"
          :disabled="!note && !isCreating"
          placeholder="Start writing your rich text note..."
        />
      </div>

      <!-- Kanban Editor -->
      <div v-else-if="currentNoteType === 'kanban'" class="kanban-editor">
        <KanbanEditor
          v-model="localContent"
          @change="onContentChange"
          :disabled="!note && !isCreating"
          placeholder="Start building your kanban board..."
        />
      </div>
    </div>

    <!-- Tags -->
    <div class="field">
      <TagInput
        v-model="localTagObjects"
        :suggestions="availableTags"
        label="Tags"
        placeholder="Add tags..."
        :allow-create="true"
        :max-tags="10"
        @tag-created="handleTagCreated"
        @tag-added="handleTagAdded"
        @tag-removed="handleTagRemoved"
      />
    </div>

    <!-- Action buttons -->
    <div class="field is-grouped">
      <div class="control">
        <button
          @click="saveNote"
          class="button is-primary"
          :class="{ 'is-loading': notesStore.isLoading }"
          :disabled="!hasChanges"
        >
          <span class="icon">
            <i class="fas fa-save"></i>
          </span>
          <span>{{ note ? 'Save Changes' : 'Create Note' }}</span>
        </button>
      </div>
      
      <div class="control" v-if="note">
        <button
          @click="cancelChanges"
          class="button"
          :disabled="!hasChanges"
        >
          Cancel
        </button>
      </div>

      <div class="control" v-if="note">
        <button
          @click="openExportModal"
          class="button is-info is-outlined"
        >
          <span class="icon">
            <i class="fas fa-download"></i>
          </span>
          <span>Export</span>
        </button>
      </div>

      <div class="control" v-if="note">
        <button
          @click="deleteNote"
          class="button is-danger is-outlined"
          :class="{ 'is-loading': notesStore.isLoading }"
        >
          <span class="icon">
            <i class="fas fa-trash"></i>
          </span>
          <span>Delete</span>
        </button>
      </div>
    </div>

    <!-- Export Modal -->
    <ExportModal
      :is-open="showExportModal"
      :note-ids="note ? [note.id] : []"
      :note-title="note?.title"
      @close="showExportModal = false"
      @exported="onExported"
    />

    <!-- Auto-save settings -->
    <div class="auto-save-settings" v-if="showSettings">
      <div class="field">
        <label class="checkbox">
          <input
            type="checkbox"
            v-model="notesStore.autoSaveEnabled"
          />
          Enable auto-save
        </label>
      </div>
      
      <div class="field" v-if="notesStore.autoSaveEnabled">
        <label class="label">Auto-save interval (seconds)</label>
        <div class="control">
          <input
            v-model.number="autoSaveSeconds"
            @change="updateAutoSaveInterval"
            class="input"
            type="number"
            min="1"
            max="60"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useNotesStore } from '../../stores/notes'
import { useAutoSave, useOfflineSync } from '../../composables/useAutoSave'
import type { Note, CreateNoteData, Tag } from '../../services/noteService'
import RichTextEditor from '../editors/RichTextEditor.vue'
import MarkdownEditor from '../editors/MarkdownEditor.vue'
import KanbanEditor from '../editors/KanbanEditor.vue'
import TagInput from '../tags/TagInput.vue'
import ExportModal from '../export/ExportModal.vue'

interface Props {
  note?: Note | null
  showAutoSaveStatus?: boolean
  showSettings?: boolean
}

interface Emits {
  (e: 'saved', note: Note): void
  (e: 'deleted', noteId: string): void
  (e: 'error', error: string): void
}

const props = withDefaults(defineProps<Props>(), {
  showAutoSaveStatus: true,
  showSettings: false
})

const emit = defineEmits<Emits>()

const notesStore = useNotesStore()
const offlineSync = useOfflineSync()

// Local state
const localTitle = ref('')
const localContent = ref('')
const localTags = ref<string[]>([])
const localTagObjects = ref<Tag[]>([])
const selectedNoteType = ref<'richtext' | 'markdown' | 'kanban'>('markdown')
const isCreating = ref(false)
const availableTags = ref<Tag[]>([])

// Auto-save settings
const autoSaveSeconds = ref(Math.floor(notesStore.autoSaveInterval / 1000))

// Export modal state
const showExportModal = ref(false)

// Computed
const currentNoteType = computed(() => {
  return props.note?.noteType || selectedNoteType.value
})

const hasChanges = computed(() => {
  if (!props.note && !isCreating.value) return false
  
  if (props.note) {
    return (
      localTitle.value !== props.note.title ||
      localContent.value !== props.note.content ||
      JSON.stringify(localTagObjects.value.map(t => t.name).sort()) !== JSON.stringify(props.note.tags.map(t => t.name).sort())
    )
  }
  
  return localTitle.value.trim() !== '' || localContent.value.trim() !== ''
})

// Auto-save composable - only initialize for existing notes with valid IDs
const autoSave = (props.note && props.note.id && typeof props.note.id === 'string' && props.note.id !== 'null') ? useAutoSave(props.note.id, {
  onSave: (data) => {
    console.log('Auto-saved:', data)
  },
  onError: (error) => {
    emit('error', error.message)
  }
}) : null

// Methods
const onTitleChange = () => {
  if (autoSave && props.note) {
    autoSave.updateTitle(localTitle.value)
  }
}

const onContentChange = () => {
  if (autoSave && props.note) {
    autoSave.updateContent(localContent.value)
  }
}

const handleTagCreated = async (tagName: string) => {
  try {
    const newTag = await notesStore.createTag(tagName)
    availableTags.value.push(newTag)
  } catch (error) {
    console.error('Failed to create tag:', error)
  }
}

const handleTagAdded = (tag: Tag) => {
  if (autoSave && props.note) {
    autoSave.updateTags(localTagObjects.value.map(t => t.name))
  }
}

const handleTagRemoved = (tag: Tag, index: number) => {
  if (autoSave && props.note) {
    autoSave.updateTags(localTagObjects.value.map(t => t.name))
  }
}

const saveNote = async () => {
  try {
    if (props.note) {
      // Update existing note
      if (autoSave) {
        await autoSave.forceSave()
      } else {
        const updatedNote = await notesStore.updateNote(props.note.id, {
          title: localTitle.value,
          content: localContent.value,
          tags: localTagObjects.value.map(t => t.name)
        })
        emit('saved', updatedNote)
      }
    } else {
      // Create new note
      const noteData: CreateNoteData = {
        title: localTitle.value || 'Untitled Note',
        content: localContent.value,
        noteType: selectedNoteType.value,
        tags: localTagObjects.value.map(t => t.name)
      }
      
      const newNote = await notesStore.createNote(noteData)
      emit('saved', newNote)
      
      // Reset form
      localTitle.value = ''
      localContent.value = ''
      localTagObjects.value = []
      isCreating.value = false
    }
  } catch (error) {
    emit('error', error instanceof Error ? error.message : 'Failed to save note')
  }
}

const cancelChanges = () => {
  if (props.note) {
    localTitle.value = props.note.title
    localContent.value = props.note.content
    localTagObjects.value = [...props.note.tags]
    
    if (autoSave) {
      autoSave.cancelAutoSave()
    }
  }
}

const deleteNote = async () => {
  if (props.note && confirm('Are you sure you want to delete this note?')) {
    try {
      await notesStore.deleteNote(props.note.id)
      emit('deleted', props.note.id)
    } catch (error) {
      emit('error', error instanceof Error ? error.message : 'Failed to delete note')
    }
  }
}

const openExportModal = () => {
  showExportModal.value = true
}

const onExported = (result: { success: boolean; filename?: string; error?: string }) => {
  if (result.success) {
    // You might want to show a success toast here
    console.log('Note exported successfully:', result.filename)
  } else {
    emit('error', result.error || 'Export failed')
  }
}

const updateAutoSaveInterval = () => {
  notesStore.setAutoSaveInterval(autoSaveSeconds.value * 1000)
}

const startCreating = () => {
  isCreating.value = true
}

// Initialize local state when note changes
watch(
  () => props.note,
  (newNote) => {
    if (newNote && newNote.id) {
      localTitle.value = newNote.title || ''
      localContent.value = newNote.content || ''
      
      // Handle tags safely - they should be Tag objects
      if (Array.isArray(newNote.tags)) {
        localTagObjects.value = newNote.tags.map(t => 
          typeof t === 'string' ? { id: '', name: t, userId: '', createdAt: '' } : t
        )
      } else {
        localTagObjects.value = []
      }
      
      if (autoSave) {
        autoSave.initialize(newNote.content || '')
      }
    } else {
      localTitle.value = ''
      localContent.value = ''
      localTagObjects.value = []
      isCreating.value = false
    }
  },
  { immediate: true }
)

// Keyboard shortcuts
const handleKeydown = (event: KeyboardEvent) => {
  // Ctrl+S or Cmd+S to save
  if ((event.ctrlKey || event.metaKey) && event.key === 's') {
    event.preventDefault()
    saveNote()
  }
}

onMounted(async () => {
  document.addEventListener('keydown', handleKeydown)
  
  // Load available tags
  try {
    availableTags.value = await notesStore.loadTags()
  } catch (error) {
    console.error('Failed to load tags:', error)
  }
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})

// Expose methods for parent components
defineExpose({
  startCreating,
  saveNote,
  cancelChanges
})
</script>

<style scoped>
.note-editor {
  margin: 0 auto;
  padding: 1rem;
}

.auto-save-status {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 1000;
}

.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-indicator.is-saving {
  background-color: #3273dc;
  color: white;
}

.status-indicator.is-saved {
  background-color: #23d160;
  color: white;
}

.status-indicator.is-error {
  background-color: #ff3860;
  color: white;
}

.offline-indicator {
  position: fixed;
  top: 4rem;
  right: 1rem;
  z-index: 1000;
}

.editor-container {
  margin: 1rem 0;
}

.textarea {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
}



.auto-save-settings {
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #dbdbdb;
}
</style>