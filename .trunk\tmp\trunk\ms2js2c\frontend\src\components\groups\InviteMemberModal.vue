<template>
  <div class="modal is-active">
    <div class="modal-background" @click="$emit('close')"></div>
    <div class="modal-card">
      <header class="modal-card-head">
        <p class="modal-card-title">Invite Member to {{ group.name }}</p>
        <button class="delete" @click="$emit('close')"></button>
      </header>
      
      <section class="modal-card-body">
        <form @submit.prevent="handleSubmit">
          <!-- Email -->
          <div class="field">
            <label class="label">Email Address *</label>
            <div class="control has-icons-left">
              <input
                v-model="form.email"
                class="input"
                :class="{ 'is-danger': errors.email }"
                type="email"
                placeholder="Enter email address"
                required
              />
              <span class="icon is-small is-left">
                <i class="fas fa-envelope"></i>
              </span>
            </div>
            <p v-if="errors.email" class="help is-danger">{{ errors.email }}</p>
          </div>

          <!-- Role -->
          <div class="field">
            <label class="label">Role *</label>
            <div class="control">
              <div class="select is-fullwidth">
                <select v-model="form.role" required>
                  <option value="">Select a role</option>
                  <option value="viewer">Viewer</option>
                  <option value="editor">Editor</option>
                  <option value="admin">Administrator</option>
                </select>
              </div>
            </div>
            <p v-if="errors.role" class="help is-danger">{{ errors.role }}</p>
          </div>

          <!-- Role descriptions -->
          <div class="field">
            <div class="content is-small">
              <h6 class="title is-6">Role Permissions:</h6>
              <ul>
                <li><strong>Viewer:</strong> Can view group notes and members</li>
                <li><strong>Editor:</strong> Can view and edit group notes</li>
                <li><strong>Administrator:</strong> Full access including member management and settings</li>
              </ul>
            </div>
          </div>

          <!-- Group settings info -->
          <div v-if="!group.settings.allowMemberInvites && userRole !== 'admin'" class="notification is-warning is-light">
            <p class="is-size-7">
              <strong>Note:</strong> This group doesn't allow member invitations. Only administrators can invite new members.
            </p>
          </div>

          <!-- Error Display -->
          <div v-if="submitError" class="notification is-danger">
            {{ submitError }}
          </div>
        </form>
      </section>
      
      <footer class="modal-card-foot">
        <button 
          class="button is-primary"
          :class="{ 'is-loading': loading }"
          :disabled="loading || !isFormValid"
          @click="handleSubmit"
        >
          Send Invitation
        </button>
        <button class="button" @click="$emit('close')">Cancel</button>
      </footer>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue';
import { useGroupsStore } from '../../stores/groups';
import { useAuthStore } from '../../stores/auth';
import type { GroupWithMembers, InviteUserData, UserRole } from '../../types/group';

// Props
const props = defineProps<{
  group: GroupWithMembers;
}>();

// Emits
const emit = defineEmits<{
  close: [];
  invited: [];
}>();

const groupsStore = useGroupsStore();
const authStore = useAuthStore();

// Form data
const form = reactive<InviteUserData>({
  email: '',
  role: 'viewer'
});

// Form state
const loading = ref(false);
const submitError = ref<string | null>(null);
const errors = reactive({
  email: '',
  role: ''
});

// Computed
const userRole = computed((): UserRole | null => {
  if (!authStore.user) return null;
  return groupsStore.getUserRole(props.group.id, authStore.user.id) as UserRole;
});

const isFormValid = computed(() => {
  return form.email.trim().length > 0 && 
         form.role.length > 0 &&
         /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email);
});

// Methods
const validateForm = (): boolean => {
  // Reset errors
  errors.email = '';
  errors.role = '';

  let isValid = true;

  // Validate email
  if (!form.email.trim()) {
    errors.email = 'Email is required';
    isValid = false;
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email)) {
    errors.email = 'Invalid email format';
    isValid = false;
  }

  // Check if user is already a member
  const existingMember = props.group.members.find(m => m.email.toLowerCase() === form.email.toLowerCase());
  if (existingMember) {
    errors.email = 'This user is already a member of the group';
    isValid = false;
  }

  // Validate role
  if (!form.role) {
    errors.role = 'Role is required';
    isValid = false;
  } else if (!['admin', 'editor', 'viewer'].includes(form.role)) {
    errors.role = 'Invalid role selected';
    isValid = false;
  }

  return isValid;
};

const handleSubmit = async () => {
  if (!validateForm()) {
    return;
  }

  try {
    loading.value = true;
    submitError.value = null;

    await groupsStore.inviteUser(props.group.id, {
      email: form.email.trim().toLowerCase(),
      role: form.role as UserRole
    });

    emit('invited');
  } catch (error: any) {
    console.error('Error inviting user:', error);
    submitError.value = error.message || 'Failed to send invitation';
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.modal-card {
  width: 90%;
  max-width: 500px;
}

.content ul {
  margin-left: 1rem;
}

.content ul li {
  margin-bottom: 0.25rem;
}
</style>