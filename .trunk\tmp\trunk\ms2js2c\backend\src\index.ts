import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';
import { initializeDatabase } from './config/database';
import { initializeRedis } from './config/redis';
import { CacheService } from './services/CacheService';
import authRoutes from './routes/auth';
import noteRoutes from './routes/notes';
import searchRoutes from './routes/search';
import groupRoutes from './routes/groups';
import userRoutes from './routes/user';
import templateRoutes from './routes/templates';
import { 
  generalRateLimit, 
  authRateLimit, 
  passwordResetRateLimit, 
  apiRateLimit, 
  uploadRateLimit,
  authSlowDown,
  apiSlowDown
} from './middleware/rateLimiting';
import { auditLoggingMiddleware, securityMonitoringMiddleware, rateLimitMonitoringMiddleware } from './middleware/auditLogging';
import { AuditLogService } from './services/AuditLogService';
import { SystemConfigService } from './services/SystemConfigService';
import { ContentModerationService } from './services/ContentModerationService';
import { 
  securityHeaders, 
  sanitizeInput, 
  sessionSecurity, 
  enhancedJWTValidation, 
  bruteForceProtection, 
  requestSizeLimit,
  csrfProtection,
  ipFilter
} from './middleware/security';
import { securityValidation, rateLimitValidation } from './middleware/enhancedValidation';
import auditRoutes from './routes/audit';
import twoFactorRoutes from './routes/twoFactor';
import adminRoutes from './routes/admin';
import performanceRoutes from './routes/performance';

// Load environment variables
dotenv.config();

// Initialize security configuration
import { initializeSecurity, securityConfig, corsConfig } from './config/security';

const app = express();
const PORT = process.env.PORT || 3001;

// Security middleware (applied first)
app.use(securityHeaders());
app.use(requestSizeLimit(10 * 1024 * 1024)); // 10MB limit
app.use(sanitizeInput());
app.use(securityValidation);

// Basic middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'", "https://apis.google.com"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "https://api.github.com", "https://accounts.google.com"],
      frameSrc: ["'self'", "https://accounts.google.com"],
      objectSrc: ["'none'"],
      baseUri: ["'self'"]
    }
  },
  crossOriginEmbedderPolicy: false // Disable for development
}));

app.use(cors(corsConfig));

app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Apply enhanced JWT validation
app.use(enhancedJWTValidation());

// Apply session security
app.use(sessionSecurity());

// Apply rate limiting validation
app.use(rateLimitValidation);

// Apply general rate limiting to all routes
app.use(generalRateLimit);
app.use(apiSlowDown);

// Apply specific rate limiting to auth endpoints
app.use('/api/auth', authRateLimit);
app.use('/api/auth', authSlowDown);
app.use('/api/auth/forgot-password', passwordResetRateLimit);
app.use('/api/auth/reset-password', passwordResetRateLimit);

// Apply API rate limiting
app.use('/api', apiRateLimit);

// Apply upload rate limiting
app.use('/api/notes/*/export', uploadRateLimit);
app.use('/api/export', uploadRateLimit);

// Apply brute force protection to auth endpoints
app.use('/api/auth', bruteForceProtection({
  maxAttempts: 5,
  windowMs: 15 * 60 * 1000, // 15 minutes
  blockDurationMs: 60 * 60 * 1000 // 1 hour
}));

// Apply CSRF protection (optional, mainly for web forms)
if (process.env.ENABLE_CSRF_PROTECTION === 'true') {
  app.use(csrfProtection());
}

// Apply IP filtering if configured
const allowedIPs = process.env.ALLOWED_IPS?.split(',') || [];
const blockedIPs = process.env.BLOCKED_IPS?.split(',') || [];
if (allowedIPs.length > 0 || blockedIPs.length > 0) {
  app.use(ipFilter({
    whitelist: allowedIPs,
    blacklist: blockedIPs,
    mode: allowedIPs.length > 0 ? 'whitelist' : 'blacklist'
  }));
}

// Apply audit logging middleware
app.use(auditLoggingMiddleware({
  logRequestBody: true,
  sensitiveFields: ['password', 'token', 'secret']
}));

// Apply security monitoring middleware
app.use(securityMonitoringMiddleware());
app.use(rateLimitMonitoringMiddleware());

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// API routes
app.get('/api', (req, res) => {
  res.json({ message: 'Note Taking App API v1.0' });
});

// Authentication routes
app.use('/api/auth', authRoutes);

// User management routes
app.use('/api/user', userRoutes);

// Note management routes
app.use('/api', noteRoutes);

// Search routes
app.use('/api', searchRoutes);

// Group management routes
app.use('/api/groups', groupRoutes);

// Template management routes
app.use('/api', templateRoutes);

// Audit log routes
app.use('/api/audit', auditRoutes);

// Two-factor authentication routes
app.use('/api/2fa', twoFactorRoutes);

// Admin routes
app.use('/api/admin', adminRoutes);

// Performance monitoring routes
app.use('/api/performance', performanceRoutes);

// Error handling middleware
app.use((err: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

// Redirect shared note URLs to frontend
app.get('/shared/:token', (req, res) => {
  const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
  const redirectUrl = `${frontendUrl}/shared/${req.params.token}`;
  res.redirect(302, redirectUrl);
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

// Initialize database and start server
async function startServer() {
  try {
    // Initialize security configuration
    initializeSecurity();
    
    await initializeDatabase();
    console.log('Database initialized successfully');
    
    // Initialize Redis and caching
    const redisClient = await initializeRedis();
    CacheService.initialize(redisClient);
    console.log('Caching service initialized successfully');
    
    // Initialize audit log service
    AuditLogService.initialize({
      retentionDays: securityConfig.audit.retentionDays
    });
    
    // Initialize system configuration service
    await SystemConfigService.initialize();
    
    // Initialize content moderation service
    await ContentModerationService.initialize();
    
    app.listen(PORT, () => {
      console.log(`Server running on port ${PORT}`);
      console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
      console.log('Security enhancements active:');
      console.log(`- Rate limiting: ${securityConfig.rateLimit.general.max} requests per ${securityConfig.rateLimit.general.windowMs / 1000}s`);
      console.log(`- CSRF protection: ${securityConfig.security.enableCSRF ? 'enabled' : 'disabled'}`);
      console.log(`- IP filtering: ${securityConfig.security.enableIPFiltering ? 'enabled' : 'disabled'}`);
      console.log(`- 2FA support: ${securityConfig.twoFactor.enabled ? 'enabled' : 'disabled'}`);
      console.log(`- Audit logging: ${securityConfig.audit.enabled ? 'enabled' : 'disabled'}`);
    });
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('SIGTERM received, shutting down gracefully...');
  AuditLogService.shutdown();
  const { closeRedis } = await import('./config/redis');
  await closeRedis();
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('SIGINT received, shutting down gracefully...');
  AuditLogService.shutdown();
  const { closeRedis } = await import('./config/redis');
  await closeRedis();
  process.exit(0);
});

startServer();