<template>
  <div class="group-notes">
    <div class="level">
      <div class="level-left">
        <div class="level-item">
          <h3 class="title is-5">Group Notes</h3>
        </div>
      </div>
      <div class="level-right">
        <div class="level-item">
          <button 
            v-if="canCreateNotes"
            class="button is-primary"
            @click="createNote"
          >
            <span class="icon">
              <i class="fas fa-plus"></i>
            </span>
            <span>New Note</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Notes will be implemented when note-group integration is added -->
    <div class="has-text-centered py-6">
      <div class="icon is-large has-text-grey-light mb-4">
        <i class="fas fa-sticky-note fa-3x"></i>
      </div>
      <h4 class="title is-6 has-text-grey">Group Notes Coming Soon</h4>
      <p class="has-text-grey">
        Group note functionality will be available once note-group integration is implemented.
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRouter } from 'vue-router';
import { getPermissions } from '../../types/group';
import type { GroupWithMembers, UserRole } from '../../types/group';

// Props
const props = defineProps<{
  group: GroupWithMembers;
  userRole: UserRole | null;
}>();

const router = useRouter();

// Computed properties
const permissions = computed(() => {
  return props.userRole ? getPermissions(props.userRole) : null;
});

const canCreateNotes = computed(() => {
  return permissions.value?.canEditNotes || false;
});

// Methods
const createNote = () => {
  // Navigate to note creation with group context
  router.push(`/notes/new?groupId=${props.group.id}`);
};
</script>

<style scoped>
/* Component-specific styles */
</style>