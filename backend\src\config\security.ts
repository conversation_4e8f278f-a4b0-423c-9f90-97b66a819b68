import dotenv from 'dotenv';

dotenv.config();

export interface SecurityConfig {
  jwt: {
    secret: string;
    refreshSecret: string;
    accessTokenExpiry: string;
    refreshTokenExpiry: string;
    issuer: string;
    audience: string;
  };
  rateLimit: {
    general: {
      windowMs: number;
      max: number;
    };
    auth: {
      windowMs: number;
      max: number;
    };
    api: {
      windowMs: number;
      max: number;
    };
    passwordReset: {
      windowMs: number;
      max: number;
    };
  };
  security: {
    enableCSRF: boolean;
    enableIPFiltering: boolean;
    allowedIPs: string[];
    blockedIPs: string[];
    maxRequestSize: number;
    sessionTimeout: number;
    bruteForce: {
      maxAttempts: number;
      windowMs: number;
      blockDurationMs: number;
    };
  };
  twoFactor: {
    enabled: boolean;
    appName: string;
    backupCodesCount: number;
    totpWindow: number;
  };
  audit: {
    enabled: boolean;
    retentionDays: number;
    logSensitiveData: boolean;
  };
}

export const securityConfig: SecurityConfig = {
  jwt: {
    secret: process.env.JWT_SECRET || (() => {
      throw new Error('JWT_SECRET environment variable is required');
    })(),
    refreshSecret: process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET + '_refresh',
    accessTokenExpiry: process.env.JWT_EXPIRES_IN || '15m',
    refreshTokenExpiry: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
    issuer: process.env.JWT_ISSUER || 'note-app',
    audience: process.env.JWT_AUDIENCE || 'note-app-users'
  },
  rateLimit: {
    general: {
      windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
      max: parseInt(process.env.RATE_LIMIT_MAX || '1000')
    },
    auth: {
      windowMs: parseInt(process.env.AUTH_RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
      max: parseInt(process.env.AUTH_RATE_LIMIT_MAX || '5')
    },
    api: {
      windowMs: parseInt(process.env.API_RATE_LIMIT_WINDOW_MS || '60000'), // 1 minute
      max: parseInt(process.env.API_RATE_LIMIT_MAX || '100')
    },
    passwordReset: {
      windowMs: parseInt(process.env.PASSWORD_RESET_RATE_LIMIT_WINDOW_MS || '3600000'), // 1 hour
      max: parseInt(process.env.PASSWORD_RESET_RATE_LIMIT_MAX || '3')
    }
  },
  security: {
    enableCSRF: process.env.ENABLE_CSRF_PROTECTION === 'true',
    enableIPFiltering: process.env.ENABLE_IP_FILTERING === 'true',
    allowedIPs: process.env.ALLOWED_IPS?.split(',').filter(ip => ip.trim()) || [],
    blockedIPs: process.env.BLOCKED_IPS?.split(',').filter(ip => ip.trim()) || [],
    maxRequestSize: parseInt(process.env.MAX_REQUEST_SIZE || '10485760'), // 10MB
    sessionTimeout: parseInt(process.env.SESSION_TIMEOUT || '900000'), // 15 minutes
    bruteForce: {
      maxAttempts: parseInt(process.env.BRUTE_FORCE_MAX_ATTEMPTS || '5'),
      windowMs: parseInt(process.env.BRUTE_FORCE_WINDOW_MS || '900000'), // 15 minutes
      blockDurationMs: parseInt(process.env.BRUTE_FORCE_BLOCK_DURATION_MS || '3600000') // 1 hour
    }
  },
  twoFactor: {
    enabled: process.env.ENABLE_2FA !== 'false', // Enabled by default
    appName: process.env.APP_NAME || 'Note Taking App',
    backupCodesCount: parseInt(process.env.BACKUP_CODES_COUNT || '10'),
    totpWindow: parseInt(process.env.TOTP_WINDOW || '30')
  },
  audit: {
    enabled: process.env.ENABLE_AUDIT_LOGGING !== 'false', // Enabled by default
    retentionDays: parseInt(process.env.AUDIT_LOG_RETENTION_DAYS || '365'),
    logSensitiveData: process.env.LOG_SENSITIVE_DATA === 'true'
  }
};

// Validation function to ensure security configuration is valid
export function validateSecurityConfig(): void {
  const errors: string[] = [];

  // JWT validation
  if (!securityConfig.jwt.secret || securityConfig.jwt.secret.length < 32) {
    errors.push('JWT_SECRET must be at least 32 characters long');
  }

  if (!securityConfig.jwt.refreshSecret || securityConfig.jwt.refreshSecret.length < 32) {
    errors.push('JWT_REFRESH_SECRET must be at least 32 characters long');
  }

  // Rate limit validation
  if (securityConfig.rateLimit.general.max < 1) {
    errors.push('General rate limit max must be at least 1');
  }

  if (securityConfig.rateLimit.auth.max < 1) {
    errors.push('Auth rate limit max must be at least 1');
  }

  // Security validation
  if (securityConfig.security.maxRequestSize < 1024) {
    errors.push('Max request size must be at least 1KB');
  }

  if (securityConfig.security.sessionTimeout < 60000) {
    errors.push('Session timeout must be at least 1 minute');
  }

  // Brute force validation
  if (securityConfig.security.bruteForce.maxAttempts < 1) {
    errors.push('Brute force max attempts must be at least 1');
  }

  if (securityConfig.security.bruteForce.windowMs < 60000) {
    errors.push('Brute force window must be at least 1 minute');
  }

  if (securityConfig.security.bruteForce.blockDurationMs < 60000) {
    errors.push('Brute force block duration must be at least 1 minute');
  }

  // Two-factor validation
  if (securityConfig.twoFactor.backupCodesCount < 5 || securityConfig.twoFactor.backupCodesCount > 20) {
    errors.push('Backup codes count must be between 5 and 20');
  }

  if (securityConfig.twoFactor.totpWindow < 15 || securityConfig.twoFactor.totpWindow > 60) {
    errors.push('TOTP window must be between 15 and 60 seconds');
  }

  // Audit validation
  if (securityConfig.audit.retentionDays < 1 || securityConfig.audit.retentionDays > 3650) {
    errors.push('Audit log retention must be between 1 and 3650 days');
  }

  if (errors.length > 0) {
    throw new Error(`Security configuration validation failed:\n${errors.join('\n')}`);
  }
}

// Environment-specific security settings
export function getEnvironmentSecuritySettings() {
  const env = process.env.NODE_ENV || 'development';
  
  const settings = {
    development: {
      strictSSL: false,
      allowInsecureConnections: true,
      debugMode: true,
      verboseLogging: true
    },
    test: {
      strictSSL: false,
      allowInsecureConnections: true,
      debugMode: true,
      verboseLogging: false
    },
    production: {
      strictSSL: true,
      allowInsecureConnections: false,
      debugMode: false,
      verboseLogging: false
    }
  };

  return settings[env as keyof typeof settings] || settings.production;
}

// Security headers configuration
export const securityHeaders = {
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'", "https://apis.google.com"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "https://api.github.com", "https://accounts.google.com"],
      frameSrc: ["'self'", "https://accounts.google.com"],
      objectSrc: ["'none'"],
      baseUri: ["'self'"],
      formAction: ["'self'"],
      frameAncestors: ["'none'"]
    }
  },
  hsts: {
    maxAge: ********, // 1 year
    includeSubDomains: true,
    preload: true
  },
  noSniff: true,
  frameguard: { action: 'deny' },
  xssFilter: true,
  referrerPolicy: 'strict-origin-when-cross-origin'
};

// CORS configuration
export const corsConfig = {
  origin: process.env.FRONTEND_URL || ['http://localhost:5173', 'http://127.0.0.1:5173'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-CSRF-Token', 'X-Requested-With'],
  exposedHeaders: ['X-RateLimit-Limit', 'X-RateLimit-Remaining', 'X-RateLimit-Reset'],
  maxAge: 86400 // 24 hours
};

// Initialize security configuration
export function initializeSecurity(): void {
  try {
    validateSecurityConfig();
    console.log('Security configuration validated successfully');
    
    const envSettings = getEnvironmentSecuritySettings();
    console.log(`Security settings for ${process.env.NODE_ENV || 'development'} environment loaded`);
    
    if (envSettings.debugMode) {
      console.log('Security debug mode enabled');
    }
    
  } catch (error) {
    console.error('Security configuration validation failed:', error);
    process.exit(1);
  }
}