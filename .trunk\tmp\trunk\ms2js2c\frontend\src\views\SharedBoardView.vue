<template>
  <div class="shared-board-view">
    <div class="container is-fluid">
      <!-- Shared Board Header -->
      <div class="shared-board-header mb-4">
        <div class="level">
          <div class="level-left">
            <div class="level-item">
              <h1 class="title is-3">
                <span class="icon">
                  <i class="fas fa-columns"></i>
                </span>
                <span>{{ boardTitle }}</span>
              </h1>
              <span class="tag is-info ml-3">
                <span class="icon">
                  <i class="fas fa-share-alt"></i>
                </span>
                <span>Shared Board</span>
              </span>
            </div>
          </div>
          <div class="level-right">
            <div class="level-item">
              <div class="buttons">
                <button
                  v-if="!isAuthenticated"
                  class="button is-primary"
                  @click="goToLogin"
                >
                  <span class="icon">
                    <i class="fas fa-sign-in-alt"></i>
                  </span>
                  <span>Sign In</span>
                </button>
                <button
                  v-else
                  class="button is-light"
                  @click="goToDashboard"
                >
                  <span class="icon">
                    <i class="fas fa-home"></i>
                  </span>
                  <span>Dashboard</span>
                </button>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Share Info -->
        <div v-if="shareInfo" class="share-info">
          <div class="notification is-light">
            <div class="media">
              <div class="media-left">
                <span class="icon is-medium">
                  <i class="fas fa-info-circle"></i>
                </span>
              </div>
              <div class="media-content">
                <p>
                  <strong>You're viewing a shared board.</strong>
                  {{ shareInfo.canEdit ? 'You can view and edit this board.' : 'You can view this board but cannot make changes.' }}
                </p>
                <p v-if="shareInfo.expiresAt" class="is-size-7 has-text-grey">
                  This share link expires on {{ formatDate(shareInfo.expiresAt) }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Password Protection -->
      <div v-if="requiresPassword && !isPasswordVerified" class="password-protection">
        <div class="card">
          <div class="card-content has-text-centered">
            <span class="icon is-large has-text-warning">
              <i class="fas fa-lock fa-2x"></i>
            </span>
            <h3 class="title is-4">Password Required</h3>
            <p class="subtitle">This shared board is password protected.</p>
            
            <form @submit.prevent="verifyPassword" class="password-form">
              <div class="field">
                <div class="control has-icons-left">
                  <input
                    v-model="password"
                    type="password"
                    class="input"
                    placeholder="Enter password"
                    :class="{ 'is-danger': passwordError }"
                    required
                  />
                  <span class="icon is-small is-left">
                    <i class="fas fa-lock"></i>
                  </span>
                </div>
                <p v-if="passwordError" class="help is-danger">{{ passwordError }}</p>
              </div>
              
              <div class="field">
                <div class="control">
                  <button
                    type="submit"
                    class="button is-primary"
                    :class="{ 'is-loading': isVerifyingPassword }"
                    :disabled="!password.trim() || isVerifyingPassword"
                  >
                    <span class="icon">
                      <i class="fas fa-unlock"></i>
                    </span>
                    <span>Access Board</span>
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>

      <!-- Loading State -->
      <div v-else-if="isLoading" class="loading-state">
        <div class="has-text-centered">
          <span class="icon is-large">
            <i class="fas fa-spinner fa-pulse"></i>
          </span>
          <p>Loading shared board...</p>
        </div>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="error-state">
        <div class="has-text-centered">
          <span class="icon is-large has-text-danger">
            <i class="fas fa-exclamation-triangle"></i>
          </span>
          <h3 class="title is-4 has-text-danger">Error Loading Board</h3>
          <p class="subtitle">{{ error }}</p>
          <div class="buttons is-centered">
            <button class="button is-primary" @click="loadSharedBoard">
              <span class="icon">
                <i class="fas fa-redo"></i>
              </span>
              <span>Retry</span>
            </button>
            <button class="button is-light" @click="goToLogin">
              <span class="icon">
                <i class="fas fa-sign-in-alt"></i>
              </span>
              <span>Sign In</span>
            </button>
          </div>
        </div>
      </div>

      <!-- Board Content -->
      <div v-else-if="board" class="board-content">
        <KanbanEditor
          :model-value="board.content"
          @update:model-value="updateBoardContent"
          :board-id="boardId"
          :read-only="!canEdit"
        />
      </div>

      <!-- Not Found State -->
      <div v-else class="not-found-state">
        <div class="has-text-centered">
          <span class="icon is-large has-text-grey-light">
            <i class="fas fa-link"></i>
          </span>
          <h3 class="title is-4 has-text-grey">Share Link Invalid</h3>
          <p class="subtitle">This share link is invalid, expired, or the board no longer exists.</p>
          <div class="buttons is-centered">
            <button class="button is-primary" @click="goToLogin">
              <span class="icon">
                <i class="fas fa-sign-in-alt"></i>
              </span>
              <span>Sign In</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import KanbanEditor from '../components/editors/KanbanEditor.vue'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// Reactive state
const board = ref<any>(null)
const shareInfo = ref<any>(null)
const isLoading = ref(true)
const error = ref<string | null>(null)
const requiresPassword = ref(false)
const isPasswordVerified = ref(false)
const password = ref('')
const passwordError = ref('')
const isVerifyingPassword = ref(false)

// Computed
const boardId = computed(() => route.params.id as string)
const shareToken = computed(() => route.params.token as string)
const boardTitle = computed(() => board.value?.title || 'Shared Board')
const isAuthenticated = computed(() => authStore.isAuthenticated)
const canEdit = computed(() => {
  if (!shareInfo.value) return false
  return shareInfo.value.canEdit && (!requiresPassword.value || isPasswordVerified.value)
})

// Methods
const loadSharedBoard = async () => {
  try {
    isLoading.value = true
    error.value = null
    
    // TODO: Replace with actual API call to load shared board
    // For now, simulate the API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Check if share token is valid (mock validation)
    if (shareToken.value === 'vlznbo4hbo') {
      // Mock shared board data
      board.value = {
        id: boardId.value,
        title: `Shared Board ${boardId.value}`,
        content: JSON.stringify({
          columns: [
            {
              id: 'todo',
              title: 'To Do',
              cards: [
                {
                  id: 'card-1',
                  title: 'Sample Task',
                  description: 'This is a sample task in the shared board',
                  labels: [],
                  assignees: [],
                  dueDate: null,
                  createdAt: new Date().toISOString()
                }
              ]
            },
            {
              id: 'in-progress',
              title: 'In Progress',
              cards: []
            },
            {
              id: 'done',
              title: 'Done',
              cards: []
            }
          ]
        }),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      
      shareInfo.value = {
        canEdit: true,
        expiresAt: null,
        requiresPassword: false
      }
      
      requiresPassword.value = shareInfo.value.requiresPassword
      isPasswordVerified.value = !requiresPassword.value
    } else {
      error.value = 'Invalid share link'
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to load shared board'
  } finally {
    isLoading.value = false
  }
}

const verifyPassword = async () => {
  try {
    isVerifyingPassword.value = true
    passwordError.value = ''
    
    // TODO: Replace with actual API call to verify password
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // Mock password verification (accept any non-empty password for demo)
    if (password.value.trim()) {
      isPasswordVerified.value = true
    } else {
      passwordError.value = 'Invalid password'
    }
  } catch (err) {
    passwordError.value = 'Failed to verify password'
  } finally {
    isVerifyingPassword.value = false
  }
}

const updateBoardContent = (content: string) => {
  if (board.value && canEdit.value) {
    board.value.content = content
    board.value.updatedAt = new Date().toISOString()
    // TODO: Save to API
  }
}

const goToLogin = () => {
  router.push({
    name: 'Login',
    query: { redirect: route.fullPath }
  })
}

const goToDashboard = () => {
  router.push('/dashboard')
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { 
    hour: '2-digit', 
    minute: '2-digit' 
  })
}

// Lifecycle
onMounted(() => {
  loadSharedBoard()
})
</script>

<style scoped>
.shared-board-view {
  min-height: 100vh;
  padding: 1rem 0;
  background: #f5f5f5;
}

.shared-board-header {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.share-info {
  margin-top: 1rem;
}

.password-protection {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.password-protection .card {
  width: 100%;
  max-width: 400px;
}

.password-form {
  margin-top: 1.5rem;
}

.board-content {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  min-height: 600px;
}

.loading-state,
.error-state,
.not-found-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Responsive design */
@media (max-width: 768px) {
  .shared-board-view {
    padding: 0.5rem 0;
  }
  
  .shared-board-header {
    margin: 0 0.5rem 1rem 0.5rem;
    padding: 1rem;
  }
  
  .board-content {
    margin: 0 0.5rem;
    padding: 0.5rem;
  }
  
  .password-protection {
    margin: 0 0.5rem;
  }
  
  .level {
    display: block;
  }
  
  .level-left,
  .level-right {
    margin-bottom: 1rem;
  }
  
  .title.is-3 {
    font-size: 1.5rem;
  }
}
</style>