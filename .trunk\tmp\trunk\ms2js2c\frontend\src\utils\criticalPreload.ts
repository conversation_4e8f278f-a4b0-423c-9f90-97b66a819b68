// Critical resource preloading for <500ms initialization
// Preload only essential resources needed for first paint

export function preloadCriticalResources(): void {
  // Preload critical chunks that will be needed immediately
  const criticalChunks = [
    'vue-runtime-core',
    'vue-reactivity'
  ]

  criticalChunks.forEach(chunk => {
    const link = document.createElement('link')
    link.rel = 'modulepreload'
    link.href = `/assets/${chunk}.js`
    document.head.appendChild(link)
  })

  // Preload critical CSS
  const criticalCSS = document.createElement('link')
  criticalCSS.rel = 'preload'
  criticalCSS.as = 'style'
  criticalCSS.href = '/assets/critical.css'
  document.head.appendChild(criticalCSS)
}

// Defer non-critical resource loading
export function scheduleNonCriticalPreload(): void {
  if ('requestIdleCallback' in window) {
    window.requestIdleCallback(() => {
      const nonCriticalChunks = [
        'vue-router-lazy',
        'pinia-lazy',
        'charts-lazy'
      ]

      nonCriticalChunks.forEach(chunk => {
        const link = document.createElement('link')
        link.rel = 'modulepreload'
        link.href = `/assets/${chunk}.js`
        document.head.appendChild(link)
      })
    }, { timeout: 3000 })
  }
}

// Initialize critical preloading
export function initializeCriticalPreload(): void {
  // Run immediately for critical resources
  preloadCriticalResources()
  
  // Schedule non-critical resources
  scheduleNonCriticalPreload()
  
  console.log('🚀 Critical resource preloading initialized')
}