import express from 'express';
import { NoteController } from '../controllers/NoteController';
import { NoteShareController } from '../controllers/NoteShareController';
import { ExportController } from '../controllers/ExportController';
import { authenticateToken } from '../middleware/auth';
import { validateRequest } from '../middleware/validation';
import { generalRateLimit } from '../middleware/rateLimiting';
import { 
  cacheNotes, 
  cacheNote, 
  cacheTags,
  invalidateNotesCache,
  invalidateNoteCache,
  invalidateTagsCache
} from '../middleware/caching';

const router = express.Router();

// Apply rate limiting to all routes
router.use(generalRateLimit);

// Public shared note access (no authentication required)
router.get('/shared/:token', NoteShareController.accessSharedNote);
router.post('/shared/:token', NoteShareController.accessSharedNote); // For password-protected shares

// Apply authentication middleware to protected routes
router.use(authenticateToken);

// Note CRUD routes
router.get('/notes', cacheNotes, NoteController.getNotes);
router.get('/notes/:id', cacheNote, NoteController.getNoteById);
router.post('/notes', validateRequest('createNote'), invalidateNotesCache, NoteController.createNote);
router.put('/notes/:id', validateRequest('updateNote'), invalidateNoteCache, NoteController.updateNote);
router.delete('/notes/:id', invalidateNoteCache, NoteController.deleteNote);

// Note version history
router.get('/notes/:id/versions', NoteController.getNoteVersions);

// Note sharing routes
router.post('/notes/:id/share', validateRequest('createShare'), NoteShareController.createShare);
router.get('/notes/:id/shares', NoteShareController.getNoteShares);

// Share management routes
router.get('/shares', NoteShareController.getUserShares);
router.put('/shares/:id', validateRequest('updateShare'), NoteShareController.updateShare);
router.delete('/shares/:id', NoteShareController.deleteShare);
router.get('/shares/:id/access-logs', NoteShareController.getShareAccessLogs);
router.post('/shares/cleanup', NoteShareController.cleanupExpiredShares);

// Tag management routes
router.get('/tags', cacheTags, NoteController.getTags);
router.post('/tags', validateRequest('createTag'), invalidateTagsCache, NoteController.createTag);

// Export routes
router.post('/notes/:id/export', validateRequest('exportNote'), ExportController.exportNote);
router.post('/notes/export', validateRequest('exportMultipleNotes'), ExportController.exportMultipleNotes);
router.get('/exports/:jobId', ExportController.getExportJob);
router.get('/exports/:jobId/download', ExportController.downloadExportJob);
router.get('/exports', ExportController.getExportHistory);
router.post('/exports/cleanup', ExportController.cleanupExportJobs);

export default router;