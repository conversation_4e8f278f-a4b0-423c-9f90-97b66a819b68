<template>
  <div class="admin-metrics">
    <div class="container is-fluid">
      <!-- Header -->
      <div class="level mb-6">
        <div class="level-left">
          <div class="level-item">
            <div>
              <h1 class="title is-3">Performance Metrics</h1>
              <p class="subtitle is-6">System performance and analytics dashboard</p>
            </div>
          </div>
        </div>
        <div class="level-right">
          <div class="level-item">
            <div class="field is-grouped">
              <div class="control">
                <div class="select">
                  <select v-model="selectedTimeRange" @change="loadMetrics">
                    <option value="1">Last Hour</option>
                    <option value="6">Last 6 Hours</option>
                    <option value="24">Last 24 Hours</option>
                    <option value="72">Last 3 Days</option>
                    <option value="168">Last Week</option>
                  </select>
                </div>
              </div>
              <div class="control">
                <button 
                  class="button is-primary" 
                  @click="loadMetrics"
                  :class="{ 'is-loading': isLoading }"
                >
                  <span class="icon">
                    <i class="fas fa-sync-alt"></i>
                  </span>
                  <span>Refresh</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Error Message -->
      <div v-if="error" class="notification is-danger mb-5">
        <button class="delete" @click="error = null"></button>
        {{ error }}
      </div>

      <!-- Loading State -->
      <div v-if="isLoading && !metrics" class="has-text-centered py-6">
        <div class="is-size-4 mb-3">
          <i class="fas fa-spinner fa-spin"></i>
        </div>
        <p>Loading performance metrics...</p>
      </div>

      <!-- Metrics Dashboard -->
      <div v-else-if="metrics">
        <!-- Summary Cards -->
        <div class="columns is-multiline mb-6">
          <div class="column is-3">
            <div class="card">
              <div class="card-content">
                <div class="media">
                  <div class="media-left">
                    <span class="icon is-large has-text-info">
                      <i class="fas fa-tachometer-alt fa-2x"></i>
                    </span>
                  </div>
                  <div class="media-content">
                    <p class="title is-4">{{ Math.round(metrics.summary.avg_response_time) }}ms</p>
                    <p class="subtitle is-6">Avg Response Time</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="column is-3">
            <div class="card">
              <div class="card-content">
                <div class="media">
                  <div class="media-left">
                    <span class="icon is-large has-text-primary">
                      <i class="fas fa-exchange-alt fa-2x"></i>
                    </span>
                  </div>
                  <div class="media-content">
                    <p class="title is-4">{{ metrics.summary.total_requests.toLocaleString() }}</p>
                    <p class="subtitle is-6">Total Requests</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="column is-3">
            <div class="card">
              <div class="card-content">
                <div class="media">
                  <div class="media-left">
                    <span class="icon is-large" :class="{
                      'has-text-success': metrics.summary.error_rate < 1,
                      'has-text-warning': metrics.summary.error_rate >= 1 && metrics.summary.error_rate < 5,
                      'has-text-danger': metrics.summary.error_rate >= 5
                    }">
                      <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </span>
                  </div>
                  <div class="media-content">
                    <p class="title is-4">{{ metrics.summary.error_rate.toFixed(2) }}%</p>
                    <p class="subtitle is-6">Error Rate</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="column is-3">
            <div class="card">
              <div class="card-content">
                <div class="media">
                  <div class="media-left">
                    <span class="icon is-large has-text-success">
                      <i class="fas fa-chart-line fa-2x"></i>
                    </span>
                  </div>
                  <div class="media-content">
                    <p class="title is-4">{{ Math.round(metrics.summary.total_requests / parseInt(selectedTimeRange)) }}</p>
                    <p class="subtitle is-6">Requests/Hour</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Charts Section -->
        <div class="columns">
          <!-- Response Time Chart -->
          <div class="column is-6">
            <div class="card">
              <header class="card-header">
                <p class="card-header-title">Response Time Trends</p>
              </header>
              <div class="card-content">
                <div class="chart-container">
                  <canvas ref="responseTimeChart"></canvas>
                </div>
              </div>
            </div>
          </div>

          <!-- Request Volume Chart -->
          <div class="column is-6">
            <div class="card">
              <header class="card-header">
                <p class="card-header-title">Request Volume</p>
              </header>
              <div class="card-content">
                <div class="chart-container">
                  <canvas ref="requestVolumeChart"></canvas>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Detailed Metrics Table -->
        <div class="card mt-5">
          <header class="card-header">
            <p class="card-header-title">Hourly Breakdown</p>
          </header>
          <div class="card-content">
            <div class="table-container">
              <table class="table is-fullwidth is-striped">
                <thead>
                  <tr>
                    <th>Time</th>
                    <th>Requests</th>
                    <th>Avg Response Time</th>
                    <th>Errors</th>
                    <th>Server Errors</th>
                    <th>Error Rate</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="metric in metrics.hourly_metrics.slice(0, 24)" :key="metric.hour">
                    <td>{{ formatHour(metric.hour) }}</td>
                    <td>{{ metric.request_count.toLocaleString() }}</td>
                    <td>{{ Math.round(metric.avg_response_time) }}ms</td>
                    <td>{{ metric.error_count }}</td>
                    <td>{{ metric.server_error_count }}</td>
                    <td>
                      <span class="tag" :class="{
                        'is-success': (metric.error_count / metric.request_count) * 100 < 1,
                        'is-warning': (metric.error_count / metric.request_count) * 100 >= 1 && (metric.error_count / metric.request_count) * 100 < 5,
                        'is-danger': (metric.error_count / metric.request_count) * 100 >= 5
                      }">
                        {{ ((metric.error_count / metric.request_count) * 100).toFixed(2) }}%
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- System Health Indicators -->
        <div class="columns mt-5">
          <div class="column is-4">
            <div class="card">
              <header class="card-header">
                <p class="card-header-title">System Health</p>
              </header>
              <div class="card-content">
                <div class="content">
                  <div class="level is-mobile">
                    <div class="level-left">
                      <div class="level-item">
                        <span class="icon is-large" :class="{
                          'has-text-success': systemHealthStatus === 'healthy',
                          'has-text-warning': systemHealthStatus === 'warning',
                          'has-text-danger': systemHealthStatus === 'critical'
                        }">
                          <i class="fas fa-heartbeat fa-2x"></i>
                        </span>
                      </div>
                    </div>
                    <div class="level-right">
                      <div class="level-item">
                        <div>
                          <p class="title is-4 text-capitalize">{{ systemHealthStatus }}</p>
                          <p class="subtitle is-6">Overall Status</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="column is-8">
            <div class="card">
              <header class="card-header">
                <p class="card-header-title">Performance Recommendations</p>
              </header>
              <div class="card-content">
                <div class="content">
                  <ul>
                    <li v-if="metrics.summary.avg_response_time > 1000" class="has-text-warning">
                      <strong>High Response Time:</strong> Average response time is above 1 second. Consider optimizing database queries or adding caching.
                    </li>
                    <li v-if="metrics.summary.error_rate > 5" class="has-text-danger">
                      <strong>High Error Rate:</strong> Error rate is above 5%. Review recent deployments and error logs.
                    </li>
                    <li v-if="metrics.summary.error_rate < 1 && metrics.summary.avg_response_time < 500" class="has-text-success">
                      <strong>Excellent Performance:</strong> System is performing optimally with low error rates and fast response times.
                    </li>
                    <li v-if="metrics.summary.total_requests / parseInt(selectedTimeRange) > 1000" class="has-text-info">
                      <strong>High Traffic:</strong> System is handling high request volume. Monitor resource usage.
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, onUnmounted } from 'vue'
import { useAdminStore } from '../stores/admin'
import { storeToRefs } from 'pinia'
import {
  Chart,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  LineController,
  BarController,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js'

// Register Chart.js components
Chart.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  LineController,
  BarController,
  Title,
  Tooltip,
  Legend,
  Filler
)

const adminStore = useAdminStore()
const { isLoading, error } = storeToRefs(adminStore)

// Local state
const metrics = ref<any>(null)
const selectedTimeRange = ref('24')
const responseTimeChart = ref<HTMLCanvasElement>()
const requestVolumeChart = ref<HTMLCanvasElement>()

// Chart instances
let responseTimeChartInstance: Chart | null = null
let requestVolumeChartInstance: Chart | null = null

const systemHealthStatus = computed(() => {
  if (!metrics.value) return 'unknown'
  
  const avgResponseTime = metrics.value.summary.avg_response_time
  const errorRate = metrics.value.summary.error_rate
  
  if (avgResponseTime < 500 && errorRate < 1) return 'healthy'
  if (avgResponseTime < 1000 && errorRate < 5) return 'warning'
  return 'critical'
})

const loadMetrics = async () => {
  try {
    const result = await adminStore.getSystemMetrics(parseInt(selectedTimeRange.value))
    if (result.success) {
      metrics.value = result.data
      await nextTick()
      renderCharts()
    }
  } catch (err) {
    console.error('Failed to load metrics:', err)
  }
}

const renderCharts = () => {
  if (!metrics.value || !responseTimeChart.value || !requestVolumeChart.value) return
  
  renderResponseTimeChart()
  renderRequestVolumeChart()
}

const renderResponseTimeChart = () => {
  if (!responseTimeChart.value || !metrics.value) return
  
  // Destroy existing chart
  if (responseTimeChartInstance) {
    responseTimeChartInstance.destroy()
  }
  
  const ctx = responseTimeChart.value.getContext('2d')!
  const data = metrics.value.hourly_metrics.slice(0, 24).reverse()
  
  responseTimeChartInstance = new Chart(ctx, {
    type: 'line',
    data: {
      labels: data.map((d: any) => formatHour(d.hour)),
      datasets: [
        {
          label: 'Average Response Time (ms)',
          data: data.map((d: any) => Math.round(d.avg_response_time)),
          borderColor: '#3273dc',
          backgroundColor: 'rgba(50, 115, 220, 0.1)',
          fill: true,
          tension: 0.4,
          pointBackgroundColor: '#3273dc',
          pointBorderColor: '#ffffff',
          pointBorderWidth: 2,
          pointRadius: 4,
          pointHoverRadius: 6
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      interaction: {
        intersect: false,
        mode: 'index'
      },
      plugins: {
        legend: {
          display: true,
          position: 'top',
          labels: {
            usePointStyle: true,
            padding: 20
          }
        },
        tooltip: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleColor: '#ffffff',
          bodyColor: '#ffffff',
          borderColor: '#3273dc',
          borderWidth: 1,
          cornerRadius: 6,
          displayColors: false,
          callbacks: {
            label: function(context) {
              return `Response Time: ${context.parsed.y}ms`
            }
          }
        }
      },
      scales: {
        x: {
          display: true,
          title: {
            display: true,
            text: 'Time',
            color: '#4a4a4a',
            font: {
              size: 12,
              weight: 'bold'
            }
          },
          grid: {
            color: 'rgba(0, 0, 0, 0.1)'
          },
          ticks: {
            color: '#4a4a4a',
            maxTicksLimit: 8
          }
        },
        y: {
          display: true,
          title: {
            display: true,
            text: 'Response Time (ms)',
            color: '#4a4a4a',
            font: {
              size: 12,
              weight: 'bold'
            }
          },
          grid: {
            color: 'rgba(0, 0, 0, 0.1)'
          },
          ticks: {
            color: '#4a4a4a',
            callback: function(value) {
              return value + 'ms'
            }
          },
          beginAtZero: true
        }
      }
    }
  })
}

const renderRequestVolumeChart = () => {
  if (!requestVolumeChart.value || !metrics.value) return
  
  // Destroy existing chart
  if (requestVolumeChartInstance) {
    requestVolumeChartInstance.destroy()
  }
  
  const ctx = requestVolumeChart.value.getContext('2d')!
  const data = metrics.value.hourly_metrics.slice(0, 24).reverse()
  
  requestVolumeChartInstance = new Chart(ctx, {
    type: 'bar',
    data: {
      labels: data.map((d: any) => formatHour(d.hour)),
      datasets: [
        {
          label: 'Request Count',
          data: data.map((d: any) => d.request_count),
          backgroundColor: 'rgba(35, 209, 96, 0.8)',
          borderColor: '#23d160',
          borderWidth: 1,
          borderRadius: 4,
          borderSkipped: false
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      interaction: {
        intersect: false,
        mode: 'index'
      },
      plugins: {
        legend: {
          display: true,
          position: 'top',
          labels: {
            usePointStyle: true,
            padding: 20
          }
        },
        tooltip: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleColor: '#ffffff',
          bodyColor: '#ffffff',
          borderColor: '#23d160',
          borderWidth: 1,
          cornerRadius: 6,
          displayColors: false,
          callbacks: {
            label: function(context) {
              return `Requests: ${context.parsed.y.toLocaleString()}`
            }
          }
        }
      },
      scales: {
        x: {
          display: true,
          title: {
            display: true,
            text: 'Time',
            color: '#4a4a4a',
            font: {
              size: 12,
              weight: 'bold'
            }
          },
          grid: {
            color: 'rgba(0, 0, 0, 0.1)'
          },
          ticks: {
            color: '#4a4a4a',
            maxTicksLimit: 8
          }
        },
        y: {
          display: true,
          title: {
            display: true,
            text: 'Request Count',
            color: '#4a4a4a',
            font: {
              size: 12,
              weight: 'bold'
            }
          },
          grid: {
            color: 'rgba(0, 0, 0, 0.1)'
          },
          ticks: {
            color: '#4a4a4a',
            callback: function(value) {
              return value.toLocaleString()
            }
          },
          beginAtZero: true
        }
      }
    }
  })
}

const formatHour = (hourString: string) => {
  const date = new Date(hourString)
  return date.toLocaleString('en-US', {
    month: 'short',
    day: 'numeric',
    hour: 'numeric',
    hour12: true
  })
}

onMounted(() => {
  loadMetrics()
})

onUnmounted(() => {
  // Clean up chart instances
  if (responseTimeChartInstance) {
    responseTimeChartInstance.destroy()
  }
  if (requestVolumeChartInstance) {
    requestVolumeChartInstance.destroy()
  }
})
</script>

<style scoped>
.admin-metrics {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 2rem 0;
}

.card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.chart-container {
  position: relative;
  height: 300px;
  width: 100%;
  padding: 1rem;
}

.chart-container canvas {
  max-width: 100%;
  height: 100% !important;
}

.table-container {
  overflow-x: auto;
}

.text-capitalize {
  text-transform: capitalize;
}

.level.is-mobile {
  margin-bottom: 0;
}
</style>