# Rich Text Editor Component

## Overview

The RichTextEditor component is a comprehensive WYSIWYG editor built with TipTap and Vue 3. It provides a full-featured rich text editing experience with a customizable toolbar and extensive formatting options.

## Features

### Basic Formatting
- **Bold** (Ctrl+B)
- **Italic** (Ctrl+I) 
- **Underline** (Ctrl+U)
- **Strikethrough**

### Headers
- H1, H2, H3 heading levels

### Lists
- Bullet lists
- Numbered lists

### Links and Images
- Link insertion with URL prompt (Ctrl+K)
- Image insertion via URL
- Image upload from local files

### Tables
- Insert tables with headers
- Add/remove columns and rows
- Delete entire tables
- Resizable columns

### Text Alignment
- Left align
- Center align
- Right align

### Text Styling
- Text color picker
- Text highlighting

### Advanced Features
- Undo/Redo functionality (Ctrl+Z, Ctrl+Y)
- Content validation and sanitization
- Keyboard shortcuts
- Responsive toolbar design

## Usage

```vue
<template>
  <RichTextEditor
    v-model="content"
    @change="handleContentChange"
    :disabled="isReadOnly"
    placeholder="Start writing..."
  />
</template>

<script setup>
import RichTextEditor from '@/components/editors/RichTextEditor.vue'
import { ref } from 'vue'

const content = ref('<p>Initial content</p>')
const isReadOnly = ref(false)

const handleContentChange = (newContent) => {
  console.log('Content changed:', newContent)
}
</script>
```

## Props

- `modelValue` (string): The HTML content of the editor
- `placeholder` (string): Placeholder text when editor is empty
- `disabled` (boolean): Whether the editor is read-only

## Events

- `update:modelValue`: Emitted when content changes
- `change`: Emitted when content changes (same as update:modelValue)

## Keyboard Shortcuts

- `Ctrl+B` / `Cmd+B`: Toggle bold
- `Ctrl+I` / `Cmd+I`: Toggle italic
- `Ctrl+U` / `Cmd+U`: Toggle underline
- `Ctrl+K` / `Cmd+K`: Insert/edit link
- `Ctrl+Z` / `Cmd+Z`: Undo
- `Ctrl+Y` / `Cmd+Y` / `Ctrl+Shift+Z`: Redo

## Security

The component includes built-in content sanitization that:
- Removes `<script>` tags
- Strips dangerous event handlers (`onclick`, etc.)
- Removes `javascript:` URLs

## Styling

The component uses BulmaCSS classes and includes comprehensive styling for:
- Toolbar buttons with hover and active states
- Editor content with proper typography
- Table styling with borders and selection
- Color picker integration
- Responsive design
# Kanban 
Editor Component

## Overview

The KanbanEditor component provides a drag-and-drop kanban board interface for organizing tasks and projects. It supports multiple columns, cards with rich content, and responsive design.

## Features

### Board Management
- Create and manage kanban boards
- Export board data as JSON
- Compact view toggle for smaller screens

### Column Management
- Add new columns
- Edit column titles (double-click to edit)
- Delete empty columns
- Reorder columns via drag-and-drop
- Card count display

### Card Management
- Add cards to columns
- Edit card title and description
- Delete cards
- Move cards between columns (drag-and-drop)
- Card selection and highlighting

### User Interface
- Responsive design (desktop → tablet → mobile)
- Toolbar with quick actions
- Modal dialogs for card editing
- Visual feedback for interactions
- Keyboard navigation support

## Usage

```vue
<template>
  <KanbanEditor
    v-model="boardData"
    @change="handleBoardChange"
    :disabled="isReadOnly"
    placeholder="Start building your kanban board..."
  />
</template>

<script setup>
import KanbanEditor from '@/components/editors/KanbanEditor.vue'
import { ref } from 'vue'

const boardData = ref('')
const isReadOnly = ref(false)

const handleBoardChange = (newBoardData) => {
  console.log('Board changed:', JSON.parse(newBoardData))
}
</script>
```

## Props

- `modelValue` (string): JSON string representing the kanban board data
- `placeholder` (string): Placeholder text when board is empty
- `disabled` (boolean): Whether the editor is read-only

## Events

- `update:modelValue`: Emitted when board data changes
- `change`: Emitted when board data changes (same as update:modelValue)

## Data Structure

The KanbanEditor works with JSON data in the following format:

```typescript
interface KanbanBoard {
  id: string
  title: string
  columns: KanbanColumn[]
  createdAt: string
  updatedAt: string
}

interface KanbanColumn {
  id: string
  title: string
  position: number
  boardId: string
  cards: KanbanCard[]
  createdAt: string
  updatedAt: string
}

interface KanbanCard {
  id: string
  title: string
  description?: string
  position: number
  columnId: string
  createdAt: string
  updatedAt: string
}
```

## Exposed Methods

The component exposes several methods for programmatic control:

```javascript
// Get reference to the component
const kanbanEditor = ref()

// Add a new column
kanbanEditor.value.addColumn()

// Add a new card (requires column selection)
kanbanEditor.value.addCard()

// Export board data
kanbanEditor.value.exportBoard()

// Get current board data
const board = kanbanEditor.value.getBoard()
```

## Responsive Behavior

- **Desktop (>768px)**: Full-width columns with all features visible
- **Tablet (≤768px)**: Slightly narrower columns, compact toolbar
- **Mobile (≤480px)**: Single column view with horizontal scrolling

## Accessibility

- Keyboard navigation support
- ARIA labels for screen readers
- Focus management for modals
- High contrast support
- Semantic HTML structure

## Styling

The component uses BulmaCSS classes and includes:
- Card-based design with shadows and hover effects
- Drag-and-drop visual feedback
- Responsive grid layout
- Modal dialogs with proper styling
- Toolbar with grouped actions
- Color-coded status indicators