<template>
  <div class="group-detail">
    <!-- Loading state -->
    <div v-if="loading" class="has-text-centered py-6">
      <div class="loader"></div>
      <p class="mt-2">Loading group...</p>
    </div>

    <!-- Error state -->
    <div v-else-if="error" class="notification is-danger">
      <button class="delete" @click="clearError"></button>
      {{ error }}
    </div>

    <!-- Group not found -->
    <div v-else-if="!group" class="has-text-centered py-6">
      <div class="icon is-large has-text-grey-light mb-4">
        <i class="fas fa-exclamation-triangle fa-3x"></i>
      </div>
      <h3 class="title is-5 has-text-grey">Group not found</h3>
      <p class="has-text-grey">The group you're looking for doesn't exist or you don't have access to it.</p>
      <router-link to="/groups" class="button is-primary mt-4">
        Back to Groups
      </router-link>
    </div>

    <!-- Group content -->
    <div v-else>
      <!-- Header -->
      <div class="level mb-5">
        <div class="level-left">
          <div class="level-item">
            <div>
              <h1 class="title is-3">{{ group.name }}</h1>
              <p v-if="group.description" class="subtitle is-5 has-text-grey">
                {{ group.description }}
              </p>
              <div class="tags">
                <span class="tag is-light">
                  <span class="icon">
                    <i class="fas fa-users"></i>
                  </span>
                  <span>{{ group.memberCount }} member{{ group.memberCount !== 1 ? 's' : '' }}</span>
                </span>
                <span 
                  class="tag"
                  :class="getRoleColor(safeUserRole)"
                >
                  {{ getRoleDisplayName(safeUserRole) }}
                </span>
              </div>
            </div>
          </div>
        </div>
        <div class="level-right">
          <div class="level-item">
            <div class="buttons">
              <button 
                v-if="canInvite"
                class="button is-primary"
                @click="showInviteModal = true"
              >
                <span class="icon">
                  <i class="fas fa-user-plus"></i>
                </span>
                <span>Invite Member</span>
              </button>
              <button 
                v-if="canEditSettings"
                class="button is-light"
                @click="showSettingsModal = true"
              >
                <span class="icon">
                  <i class="fas fa-cog"></i>
                </span>
                <span>Settings</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Tabs -->
      <div class="tabs">
        <ul>
          <li :class="{ 'is-active': activeTab === 'members' }">
            <a @click="activeTab = 'members'">
              <span class="icon is-small">
                <i class="fas fa-users"></i>
              </span>
              <span>Members</span>
            </a>
          </li>
          <li :class="{ 'is-active': activeTab === 'notes' }">
            <a @click="activeTab = 'notes'">
              <span class="icon is-small">
                <i class="fas fa-sticky-note"></i>
              </span>
              <span>Notes</span>
            </a>
          </li>
          <li v-if="canEditSettings" :class="{ 'is-active': activeTab === 'invitations' }">
            <a @click="activeTab = 'invitations'">
              <span class="icon is-small">
                <i class="fas fa-envelope"></i>
              </span>
              <span>Invitations</span>
            </a>
          </li>
        </ul>
      </div>

      <!-- Tab Content -->
      <div class="tab-content">
        <!-- Members Tab -->
        <div v-if="activeTab === 'members'">
          <GroupMembers 
            :group="group"
            :user-role="safeUserRole"
            @member-removed="onMemberRemoved"
            @role-updated="onRoleUpdated"
          />
        </div>

        <!-- Notes Tab -->
        <div v-else-if="activeTab === 'notes'">
          <GroupNotes 
            :group="group"
            :user-role="safeUserRole"
          />
        </div>

        <!-- Invitations Tab -->
        <div v-else-if="activeTab === 'invitations' && canEditSettings">
          <GroupInvitations 
            :group="group"
            @invitation-sent="onInvitationSent"
          />
        </div>
      </div>
    </div>

    <!-- Modals -->
    <InviteMemberModal 
      v-if="showInviteModal && group"
      :group="group"
      @close="showInviteModal = false"
      @invited="onMemberInvited"
    />

    <GroupSettingsModal 
      v-if="showSettingsModal && group"
      :group="group"
      @close="showSettingsModal = false"
      @updated="onGroupUpdated"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useGroupsStore } from '../../stores/groups';
import { useAuthStore } from '../../stores/auth';
import { getRoleDisplayName, getRoleColor, getPermissions } from '../../types/group';
import type { GroupWithMembers, UserRole } from '../../types/group';
import GroupMembers from './GroupMembers.vue';
import GroupNotes from './GroupNotes.vue';
import GroupInvitations from './GroupInvitations.vue';
import InviteMemberModal from './InviteMemberModal.vue';
import GroupSettingsModal from './GroupSettingsModal.vue';

const route = useRoute();
const groupsStore = useGroupsStore();
const authStore = useAuthStore();

const activeTab = ref('members');
const showInviteModal = ref(false);
const showSettingsModal = ref(false);

// Computed properties
const group = computed(() => groupsStore.currentGroup);
const loading = computed(() => groupsStore.loading);
const error = computed(() => groupsStore.error);

const userRole = computed((): UserRole | null => {
  if (!group.value || !authStore.user) return null;
  return groupsStore.getUserRole(group.value.id, authStore.user.id) as UserRole;
});

const safeUserRole = computed<UserRole>(() => userRole.value ?? 'viewer')

const permissions = computed(() => {
  return safeUserRole.value ? getPermissions(safeUserRole.value) : null;
});

const canInvite = computed(() => permissions.value?.canInvite || false);
const canEditSettings = computed(() => permissions.value?.canEditSettings || false);

// Methods
const clearError = () => {
  groupsStore.clearError();
};

const onMemberRemoved = () => {
  // Refresh group data
  if (group.value) {
    groupsStore.loadGroup(group.value.id);
  }
};

const onRoleUpdated = () => {
  // Refresh group data
  if (group.value) {
    groupsStore.loadGroup(group.value.id);
  }
};

const onMemberInvited = () => {
  showInviteModal.value = false;
  // Optionally refresh invitations if on that tab
  if (activeTab.value === 'invitations') {
    // This would trigger a refresh in the GroupInvitations component
  }
};

const onInvitationSent = () => {
  // Handle invitation sent event
};

const onGroupUpdated = (updatedGroup: GroupWithMembers) => {
  showSettingsModal.value = false;
  // Group is automatically updated in the store
};

// Watch for route changes
watch(() => route.params.id, async (newId) => {
  if (newId && typeof newId === 'string') {
    try {
      await groupsStore.loadGroup(newId);
    } catch (error) {
      console.error('Failed to load group:', error);
    }
  }
}, { immediate: true });

// Lifecycle
onMounted(async () => {
  const groupId = route.params.id as string;
  if (groupId) {
    try {
      await groupsStore.loadGroup(groupId);
    } catch (error) {
      console.error('Failed to load group:', error);
    }
  }
});
</script>

<style scoped>
.loader {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 2s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.tab-content {
  padding-top: 1.5rem;
}

.tags {
  margin-top: 0.5rem;
}

.tags .tag {
  margin-right: 0.5rem;
}
</style>