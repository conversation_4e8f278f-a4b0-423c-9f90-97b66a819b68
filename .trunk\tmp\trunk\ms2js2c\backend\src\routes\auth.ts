import { Router } from 'express';
import { AuthController } from '../controllers/AuthController';
import { 
  validateRegistration, 
  validateLogin, 
  validatePasswordReset, 
  validatePasswordResetConfirm,
  validateRefreshToken,
  validateGoogleAuth
} from '../middleware/validation';
import { 
  authRateLimit, 
  passwordResetRateLimit 
} from '../middleware/rateLimiting';
import { authenticateToken } from '../middleware/auth';

const router = Router();

// Public routes
router.post('/register', 
  authRateLimit,
  validateRegistration, 
  AuthController.register
);

router.post('/login', 
  authRateLimit,
  validateLogin, 
  AuthController.login
);

router.post('/refresh-token', 
  validateRefreshToken,
  AuthController.refreshToken
);

router.post('/forgot-password', 
  passwordResetRateLimit,
  validatePasswordReset, 
  AuthController.requestPasswordReset
);

router.post('/reset-password', 
  validatePasswordResetConfirm,
  AuthController.resetPassword
);

router.get('/verify-email/:token', 
  AuthController.verifyEmail
);

// Google OAuth routes
router.post('/google', 
  authRateLimit,
  validateGoogleAuth,
  AuthController.googleAuth
);

router.get('/google/url', 
  AuthController.googleAuthUrl
);

router.get('/google/callback', 
  AuthController.googleCallback
);

// Protected routes (require authentication)
router.post('/resend-verification', 
  authenticateToken,
  AuthController.resendVerificationEmail as any
);

router.get('/profile', 
  authenticateToken,
  AuthController.getProfile as any
);

router.post('/logout', 
  authenticateToken,
  AuthController.logout as any
);

export default router;