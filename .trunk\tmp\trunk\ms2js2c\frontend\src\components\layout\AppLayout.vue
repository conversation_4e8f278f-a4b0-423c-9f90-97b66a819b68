<template>
  <div class="app-layout" :class="layoutClasses">
    <!-- Mobile Header -->
    <header class="mobile-header is-hidden-tablet">
      <nav class="navbar is-primary">
        <div class="navbar-brand">
          <div class="navbar-item" @click="goToDashboard" style="cursor: pointer;">
            <h1 class="title is-5 has-text-white">Notes</h1>
          </div>
          <div class="navbar-burger" @click="toggleMobileSidebar">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </nav>
    </header>

    <!-- Main Layout Container -->
    <div class="layout-container">
      <!-- Sidebar Panel -->
      <aside 
        class="sidebar-panel" 
        :class="{ 'is-active': isSidebarOpen }"
      >
        <Sidebar 
          :is-collapsed="isSidebarCollapsed"
          @toggle-collapse="toggleSidebarCollapse"
          @close-mobile="closeMobileSidebar"
          @create-note="handleCreateNote"
          @open-settings="openSettingsModal"
        />
      </aside>

      <!-- Note List Panel -->
      <section 
        class="notelist-panel"
        :class="{ 'is-hidden': isEditorFullscreen }"
      >
        <!-- Breadcrumb Navigation -->
        <BreadcrumbNavigation />
        
        <NoteList 
          :section="currentSection"
          @note-selected="handleNoteSelected"
          @toggle-editor="toggleEditorFullscreen"
          @create-note="handleCreateNote"
          @share-note="handleShareNote"
        />
      </section>

      <!-- Editor Panel -->
      <main 
        class="editor-panel"
        :class="{ 'is-fullscreen': isEditorFullscreen }"
      >
        <EditorPanel 
          :selected-note="selectedNote"
          :is-fullscreen="isEditorFullscreen"
          @toggle-fullscreen="toggleEditorFullscreen"
          @create-note="handleCreateNote"
          @note-saved="handleNoteSaved"
          @share-note="handleShareNote"
        />
      </main>
    </div>

    <!-- Mobile Overlay -->
    <div 
      v-if="isSidebarOpen" 
      class="mobile-overlay is-hidden-tablet"
      @click="closeMobileSidebar"
    ></div>

    <!-- Global Components -->
    <KeyboardShortcuts 
      @open-search="openSearchModal"
      @create-note="createNote"
      @toggle-sidebar="toggleMobileSidebar"
      @focus-editor="focusEditor"
      @save-note="saveCurrentNote"
    />

    <!-- Search Modal -->
    <SearchModal 
      :is-open="isSearchModalOpen"
      @close="closeSearchModal"
      @note-selected="handleSearchNoteSelected"
      @create-note="handleCreateNote"
    />

    <!-- Share Modal -->
    <ShareModal 
      :is-open="isShareModalOpen"
      :note="noteToShare"
      @close="closeShareModal"
      @share-created="handleShareCreated"
    />

    <!-- Settings Modal -->
    <SettingsModal 
      :is-open="isSettingsModalOpen"
      @close="closeSettingsModal"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '../../stores/auth'
import Sidebar from './Sidebar.vue'
import NoteList from './NoteList.vue'
import EditorPanel from './EditorPanel.vue'
import BreadcrumbNavigation from '../navigation/BreadcrumbNavigation.vue'
import KeyboardShortcuts from '../navigation/KeyboardShortcuts.vue'
import SearchModal from '../navigation/SearchModal.vue'
import ShareModal from '../sharing/ShareModal.vue'
import SettingsModal from '../settings/SettingsModal.vue'

// Composables - with safe initialization
let route: any = null
let router: any = null
const authStore = useAuthStore()

// Reactive state
const isSidebarOpen = ref(false)
const isSidebarCollapsed = ref(false)
const isEditorFullscreen = ref(false)
const selectedNote = ref<any>(null)
const screenWidth = ref(window.innerWidth)
const isSearchModalOpen = ref(false)
const isShareModalOpen = ref(false)
const isSettingsModalOpen = ref(false)
const noteToShare = ref<any>(null)
const routerReady = ref(false)

// Safe router initialization
const initializeRouter = () => {
  try {
    route = useRoute()
    router = useRouter()
    
    if (route && router) {
      routerReady.value = true
      console.log('Router initialized successfully')
    } else {
      console.warn('Router composables not fully available')
    }
  } catch (error) {
    console.warn('Router initialization error:', error)
    // Continue without router - use window.location as fallback
  }
}

// Try to initialize router immediately
try {
  initializeRouter()
} catch (error) {
  console.warn('Initial router setup failed, will retry:', error)
}

// Check if any modal should be opened based on route or user preferences
const checkModalStates = () => {
  try {
    // Check route first
    let routePath = window.location.pathname
    
    if (routerReady.value && route?.path) {
      routePath = route.path
    } else if (router?.currentRoute?.value?.path) {
      routePath = router.currentRoute.value.path
    }
    
    // Check if we should open settings modal
    if (routePath.includes('/settings')) {
      isSettingsModalOpen.value = true
      return
    }
    
    // Don't open modals automatically after login unless specifically requested
    // This prevents the settings modal from opening automatically after login
    const isLoginPath = routePath.includes('/login') || routePath.includes('/register') || routePath.includes('/auth')
    if (isLoginPath) {
      return
    }
    
    // Load user preferences for modals
    const modalPreferences = authStore.loadModalPreferences()
    
    // Check if settings modal should be opened
    if (modalPreferences.settingsModalOpen) {
      // Only open if not on login/register page
      isSettingsModalOpen.value = true
    } else if (modalPreferences.searchModalOpen) {
      isSearchModalOpen.value = true
    } else if (modalPreferences.shareModalOpen && noteToShare.value) {
      isShareModalOpen.value = true
    }
  } catch (error) {
    console.warn('Failed to check modal states:', error)
  }
}

// Watch route changes
const routeWatcher = watch(
  () => routerReady.value && route?.path,
  (newPath) => {
    if (newPath && newPath.includes('/settings')) {
      openSettingsModal()
    }
  }
)

// Computed properties
const layoutClasses = computed(() => ({
  'is-mobile': screenWidth.value < 769,
  'is-tablet': screenWidth.value >= 769 && screenWidth.value < 1024,
  'is-desktop': screenWidth.value >= 1024,
  'sidebar-collapsed': isSidebarCollapsed.value,
  'editor-fullscreen': isEditorFullscreen.value
}))

const currentSection = computed(() => {
  // Always use window.location as primary source for reliability
  try {
    let routePath = window.location.pathname
    
    // Try to get from Vue Router if available
    if (routerReady.value && route?.path) {
      routePath = route.path
    } else if (router?.currentRoute?.value?.path) {
      routePath = router.currentRoute.value.path
    }
    
    // Ensure we have a valid path
    if (!routePath || typeof routePath !== 'string') {
      routePath = window.location.pathname || '/dashboard'
    }
    
    // Route matching logic
    if (routePath.includes('/recent')) return 'recent'
    if (routePath.includes('/favorites')) return 'favorites'
    if (routePath.includes('/archived')) return 'archived'
    if (routePath.includes('/shared')) return 'shared'
    if (routePath.includes('/notes')) return 'all-notes'
    if (routePath === '/dashboard' || routePath === '/') return 'dashboard'
    
    return 'dashboard' // Safe default
  } catch (error) {
    console.warn('Route detection error:', error)
    return 'dashboard'
  }
})

// Methods
const toggleMobileSidebar = () => {
  isSidebarOpen.value = !isSidebarOpen.value
}

const closeMobileSidebar = () => {
  isSidebarOpen.value = false
}

const toggleSidebarCollapse = () => {
  isSidebarCollapsed.value = !isSidebarCollapsed.value
}

const toggleEditorFullscreen = () => {
  isEditorFullscreen.value = !isEditorFullscreen.value
}

const handleNoteSelected = (note: any) => {
  selectedNote.value = note
  // On mobile, close sidebar when note is selected
  if (screenWidth.value < 769) {
    closeMobileSidebar()
  }
}

const handleResize = () => {
  screenWidth.value = window.innerWidth
  
  // Auto-collapse sidebar on tablets
  if (screenWidth.value >= 769 && screenWidth.value < 1024) {
    isSidebarCollapsed.value = true
  } else if (screenWidth.value >= 1024) {
    isSidebarCollapsed.value = false
  }
}

const handleCreateNote = (type?: string) => {
  createNote(type)
}

const createNote = async (type: string = 'richtext') => {
  // Create new note logic
  console.log('Creating new note:', type)
  
  // This would typically call the notes store to create a new note
  // For now, just create a placeholder
  const newNote = {
    id: `new-${Date.now()}`,
    title: 'New Note',
    content: '',
    noteType: type,
    isNew: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
  
  // Set the newly created note as selected
  selectedNote.value = newNote
}

const handleNoteSaved = (note: any) => {
  // Update the selected note with saved data
  selectedNote.value = note
}

const focusEditor = () => {
  // Focus the editor if a note is selected
  if (selectedNote.value) {
    const editorElement = document.querySelector('.editor-area, .editor-textarea')
    if (editorElement) {
      (editorElement as HTMLElement).focus()
    }
  }
}

const saveCurrentNote = () => {
  // Save the current note if one is selected
  if (selectedNote.value) {
    console.log('Save current note:', selectedNote.value.id)
    // This would trigger the save functionality
  }
}

const handleShareNote = (note: any) => {
  noteToShare.value = note
  isShareModalOpen.value = true
  // Save state to user preferences
  authStore.saveModalPreferences({ shareModalOpen: true })
}

const closeShareModal = () => {
  isShareModalOpen.value = false
  noteToShare.value = null
  // Save state to user preferences
  authStore.saveModalPreferences({ shareModalOpen: false })
}

const handleShareCreated = (share: any) => {
  console.log('Share created:', share)
  // You might want to show a success notification here
}

const openSettingsModal = () => {
  isSettingsModalOpen.value = true
  // Save state to user preferences
  authStore.saveModalPreferences({ settingsModalOpen: true })
}

const closeSettingsModal = () => {
  isSettingsModalOpen.value = false
  // Save state to user preferences
  authStore.saveModalPreferences({ settingsModalOpen: false })
}

// Reset all modal states
const resetModalStates = () => {
  try {
    // Close all modals
    isSettingsModalOpen.value = false
    isSearchModalOpen.value = false
    isShareModalOpen.value = false
    noteToShare.value = null
    
    // Clear all modal states from user preferences
    authStore.saveModalPreferences({ 
      settingsModalOpen: false,
      searchModalOpen: false,
      shareModalOpen: false
    })
  } catch (error) {
    console.warn('Failed to reset modal states:', error)
  }
}

// Apply user theme preference
const applyTheme = () => {
  try {
    const savedTheme = authStore.loadThemePreference()
    const html = document.documentElement
    
    if (savedTheme === 'dark') {
      html.classList.add('dark')
      html.classList.remove('light')
    } else if (savedTheme === 'light') {
      html.classList.add('light')
      html.classList.remove('dark')
    } else {
      // Auto mode - use system preference
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      html.classList.toggle('dark', prefersDark)
      html.classList.toggle('light', !prefersDark)
    }
  } catch (error) {
    console.warn('Failed to apply theme:', error)
    // Default to light theme on error
    const html = document.documentElement
    html.classList.add('light')
  }
}

// Emergency navigation function
const goToDashboard = () => {
  try {
    // Reset all modal states
    resetModalStates()
    
    // Ensure auth store is properly initialized before navigation
    if (!authStore.isInitialized && authStore.token) {
      console.log('Initializing auth before dashboard navigation...')
      authStore.initializeAuth()
    }
    
    if (router && router.push) {
      router.push('/dashboard')
    } else {
      // Fallback: direct navigation
      window.location.href = '/dashboard'
    }
  } catch (error) {
    console.error('Navigation error, using fallback:', error)
    window.location.href = '/dashboard'
  }
}

const openSearchModal = () => {
  isSearchModalOpen.value = true
  // Save state to user preferences
  authStore.saveModalPreferences({ searchModalOpen: true })
}

const closeSearchModal = () => {
  isSearchModalOpen.value = false
  // Save state to user preferences
  authStore.saveModalPreferences({ searchModalOpen: false })
}

const handleSearchNoteSelected = (note: any) => {
  selectedNote.value = note
  closeSearchModal()
}

// Lifecycle
onMounted(() => {
  window.addEventListener('resize', handleResize)
  
  // Set initial sidebar state based on screen size
  if (screenWidth.value >= 1024) {
    isSidebarCollapsed.value = false
  } else if (screenWidth.value >= 769) {
    isSidebarCollapsed.value = true
  }
  
  // Initialize router if not already done
  if (!routerReady.value) {
    nextTick(() => {
      initializeRouter()
    })
  }
  
  // Check if any modal should be opened
  nextTick(() => {
    checkModalStates()
  })
  
  // Apply user theme preference
  applyTheme()
  
  // Listen for theme changes
  window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', applyTheme)
  
  // Listen for logout events to clear modal states
  const logoutHandler = () => {
    try {
      // Clear all modal states from localStorage
      localStorage.removeItem('settingsModalOpen')
      localStorage.removeItem('searchModalOpen')
      localStorage.removeItem('shareModalOpen')
      
      // Close all modals
      isSettingsModalOpen.value = false
      isSearchModalOpen.value = false
      isShareModalOpen.value = false
      noteToShare.value = null
    } catch (error) {
      console.warn('Failed to clear modal states on logout:', error)
    }
  }
  
  window.addEventListener('auth-logout-complete', logoutHandler)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  window.matchMedia('(prefers-color-scheme: dark)').removeEventListener('change', applyTheme)
})
</script>

<style scoped>
.app-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

/* Mobile Header */
.mobile-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 40;
}

/* Layout Container */
.layout-container {
  display: flex;
  flex: 1;
  overflow: hidden;
  padding-top: 52px; /* Height of mobile header */
}

@media (min-width: 769px) {
  .layout-container {
    padding-top: 0;
  }
}

/* Sidebar Panel */
.sidebar-panel {
  width: 260px;
  height: 100%;
  background: #f8f9fa;
  border-right: 1px solid #e9ecef;
  overflow-y: auto;
  transition: width 0.3s ease;
  flex-shrink: 0;
}

/* Mobile sidebar behavior */
@media (max-width: 768px) {
  .sidebar-panel {
    position: fixed;
    top: 52px;
    left: -260px;
    bottom: 0;
    z-index: 30;
    transition: left 0.3s ease;
  }
  
  .sidebar-panel.is-active {
    left: 0;
  }
}

/* Collapsed sidebar */
.sidebar-collapsed .sidebar-panel {
  width: 60px;
}

/* Note List Panel */
.notelist-panel {
  width: 360px;
  height: 100%;
  background: white;
  border-right: 1px solid #e9ecef;
  overflow-y: auto;
  flex-shrink: 0;
}

/* Hide note list on mobile */
@media (max-width: 768px) {
  .notelist-panel {
    display: none;
  }
}

/* Hide note list when editor is fullscreen */
.notelist-panel.is-hidden {
  display: none;
}

/* Editor Panel */
.editor-panel {
  flex: 1;
  height: 100%;
  background: white;
  overflow-y: auto;
}

/* Fullscreen editor */
.editor-panel.is-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 50;
}

/* Mobile Overlay */
.mobile-overlay {
  position: fixed;
  top: 52px;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 25;
}

/* Responsive adjustments */
@media (min-width: 769px) and (max-width: 1023px) {
  .notelist-panel {
    width: 300px;
  }
}

@media (min-width: 1024px) {
  .notelist-panel {
    width: 360px;
  }
}

/* Scrollbar styling */
.sidebar-panel::-webkit-scrollbar,
.notelist-panel::-webkit-scrollbar,
.editor-panel::-webkit-scrollbar {
  width: 8px;
}

.sidebar-panel::-webkit-scrollbar-track,
.notelist-panel::-webkit-scrollbar-track,
.editor-panel::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.sidebar-panel::-webkit-scrollbar-thumb,
.notelist-panel::-webkit-scrollbar-thumb,
.editor-panel::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.sidebar-panel::-webkit-scrollbar-thumb:hover,
.notelist-panel::-webkit-scrollbar-thumb:hover,
.editor-panel::-webkit-scrollbar-thumb:hover {
  background: #555;
}
</style>
